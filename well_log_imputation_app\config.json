{"data_settings": {"las_folder": "./data/las_files/", "selected_logs": ["GR", "RHOB", "NPHI", "DTC"], "std_names": {"GR": "GR", "RHOB": "RHOB", "NPHI": "NPHI", "DTC": "DTC"}}, "processing_settings": {"slice_size": 256, "slice_stride": 128}, "model_settings": {"name": "autoencoder", "hyperparameters": {"autoencoder": {"epochs": 50, "patience": 10, "batch_size": 32}, "unet": {"epochs": 50, "patience": 10, "batch_size": 32}, "rf": {}, "xgboost": {}, "svm": {}, "locf": {}, "saits": {"epochs": 100, "patience": 10, "batch_size": 32, "n_layers": 2, "d_model": 256, "d_inner": 128, "n_head": 4, "d_k": 64, "d_v": 64, "dropout": 0.1}, "transformer": {"epochs": 100, "patience": 10, "batch_size": 32, "n_layers": 2, "d_model": 256, "d_inner": 128, "n_head": 4, "d_k": 64, "d_v": 64, "dropout": 0.1}, "brits": {"epochs": 100, "patience": 10, "batch_size": 32, "rnn_hidden_size": 64}, "mrnn": {"epochs": 100, "patience": 10, "batch_size": 32, "rnn_hidden_size": 64}}}, "output_settings": {"save_path": "./results/", "report_name": "imputation_report.pdf"}}