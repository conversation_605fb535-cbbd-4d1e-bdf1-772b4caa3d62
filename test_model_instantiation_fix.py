#!/usr/bin/env python3
"""
Test script to verify that all model instantiation fixes work correctly.
"""

import sys
import os
from pathlib import Path

def test_saits_instantiation():
    """Test that SAITS model can be instantiated correctly."""
    print("🔍 Testing SAITS model instantiation...")
    
    try:
        from models import Factory
        from pypots.optim import Adam
        
        # Create a factory with SAITS model
        factory = Factory(
            model='saits',
            seq_len=128,
            n_features=4,
            batch_size=32,
            epochs=1,
            patience=1,
            optimizer=Adam(lr=0.001),
            device='cpu',
            output_dir='test_output'
        )
        
        # Try to instantiate the model
        model = factory.instantiate()
        
        print(f"✅ SAITS model instantiated successfully!")
        print(f"   Model type: {type(model)}")
        print(f"   Model device: {model.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ SAITS model instantiation failed: {e}")
        return False

def test_transformer_instantiation():
    """Test that Transformer model can be instantiated correctly."""
    print("\n🔍 Testing Transformer model instantiation...")
    
    try:
        from models import Factory
        from pypots.optim import Adam
        
        # Create a factory with Transformer model
        factory = Factory(
            model='transformer',
            seq_len=128,
            n_features=4,
            batch_size=32,
            epochs=1,
            patience=1,
            optimizer=Adam(lr=0.001),
            device='cpu',
            output_dir='test_output'
        )
        
        # Try to instantiate the model
        model = factory.instantiate()
        
        print(f"✅ Transformer model instantiated successfully!")
        print(f"   Model type: {type(model)}")
        print(f"   Model device: {model.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ Transformer model instantiation failed: {e}")
        return False

def test_brits_instantiation():
    """Test that BRITS model can be instantiated correctly."""
    print("\n🔍 Testing BRITS model instantiation...")
    
    try:
        from models import Factory
        from pypots.optim import Adam
        
        # Create a factory with BRITS model
        factory = Factory(
            model='brits',
            seq_len=128,
            n_features=4,
            batch_size=32,
            epochs=1,
            patience=1,
            optimizer=Adam(lr=0.001),
            device='cpu',
            output_dir='test_output'
        )
        
        # Try to instantiate the model
        model = factory.instantiate()
        
        print(f"✅ BRITS model instantiated successfully!")
        print(f"   Model type: {type(model)}")
        print(f"   Model device: {model.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ BRITS model instantiation failed: {e}")
        return False

def test_mrnn_instantiation():
    """Test that MRNN model can be instantiated correctly."""
    print("\n🔍 Testing MRNN model instantiation...")
    
    try:
        from models import Factory
        from pypots.optim import Adam
        
        # Create a factory with MRNN model
        factory = Factory(
            model='mrnn',
            seq_len=128,
            n_features=4,
            batch_size=32,
            epochs=1,
            patience=1,
            optimizer=Adam(lr=0.001),
            device='cpu',
            output_dir='test_output'
        )
        
        # Try to instantiate the model
        model = factory.instantiate()
        
        print(f"✅ MRNN model instantiated successfully!")
        print(f"   Model type: {type(model)}")
        print(f"   Model device: {model.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ MRNN model instantiation failed: {e}")
        return False

def test_locf_instantiation():
    """Test that LOCF model can be instantiated correctly."""
    print("\n🔍 Testing LOCF model instantiation...")
    
    try:
        from models import Factory
        from pypots.optim import Adam
        
        # Create a factory with LOCF model
        factory = Factory(
            model='locf',
            seq_len=128,
            n_features=4,
            batch_size=32,
            epochs=1,
            patience=1,
            optimizer=Adam(lr=0.001),
            device='cpu',
            output_dir='test_output'
        )
        
        # Try to instantiate the model
        model = factory.instantiate()
        
        print(f"✅ LOCF model instantiated successfully!")
        print(f"   Model type: {type(model)}")
        
        return True
        
    except Exception as e:
        print(f"❌ LOCF model instantiation failed: {e}")
        return False

def test_main_with_saits():
    """Test that main.py can run with SAITS model."""
    print("\n🔍 Testing main.py with SAITS model...")
    
    try:
        # Reset sys.argv to test with SAITS
        original_argv = sys.argv.copy()
        sys.argv = [
            'test_script',
            '--model', 'saits',
            '--epochs', '1',
            '--n_folds', '1',
            '--dataset_name', 'real_las_dataset'
        ]
        
        from cfg import Configs
        cfg = Configs()
        args = cfg.parse_args()
        
        print(f"✅ Configuration with SAITS parsed successfully!")
        print(f"   Model: {args.model}")
        print(f"   Dataset: {args.dataset_name}")
        print(f"   Epochs: {args.epochs}")
        
        # Test model instantiation through main.py workflow
        from main import select_optimizer
        from models import Factory
        
        optimizer = select_optimizer(args.optimizer, args.lr)
        factory = Factory(
            model=args.model,
            seq_len=args.seq_len,
            n_features=args.n_features,
            batch_size=args.batch_size,
            epochs=args.epochs,
            patience=args.patience,
            optimizer=optimizer,
            device=args.device,
            output_dir=args.output_dir
        )
        
        model = factory.instantiate()
        print(f"✅ SAITS model instantiated through main.py workflow!")
        
        # Restore original argv
        sys.argv = original_argv
        return True
        
    except SystemExit as e:
        print(f"❌ Configuration failed with exit: {e}")
        return False
    except Exception as e:
        print(f"❌ Main.py with SAITS failed: {e}")
        return False

def cleanup_test_output():
    """Clean up test output directory."""
    try:
        import shutil
        if Path("test_output").exists():
            shutil.rmtree("test_output")
    except:
        pass

def main():
    """Run all tests."""
    print("🚀 Testing model instantiation fixes...\n")
    
    # Clean up before tests
    cleanup_test_output()
    
    tests = [
        ("SAITS Model", test_saits_instantiation),
        ("Transformer Model", test_transformer_instantiation),
        ("BRITS Model", test_brits_instantiation),
        ("MRNN Model", test_mrnn_instantiation),
        ("LOCF Model", test_locf_instantiation),
        ("Main.py with SAITS", test_main_with_saits),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"Running {test_name} Test")
        print(f"{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Clean up after tests
    cleanup_test_output()
    
    # Summary
    print(f"{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! Model instantiation fixes are working correctly.")
        print("✅ SAITS model TypeError has been resolved.")
        print("✅ Transformer model d_inner parameter has been fixed.")
        print("✅ All PyPOTS models can be instantiated successfully.")
        print("✅ main.py can now run experiments with SAITS and other models.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print(f"{'='*60}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
