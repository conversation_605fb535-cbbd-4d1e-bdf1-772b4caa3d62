#!/usr/bin/env python3
"""
Test script for the refactored well log imputation implementation.
This script validates the high-priority refactoring components.
"""

import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, Any

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_synthetic_test_data() -> pd.DataFrame:
    """Create synthetic well log data for testing."""
    print("🔧 Creating synthetic test data...")
    
    np.random.seed(42)
    
    wells = ['WELL_A', 'WELL_B', 'WELL_C']
    data_points_per_well = 200
    
    all_data = []
    
    for well in wells:
        # Create depth column
        depths = np.linspace(1000, 1000 + data_points_per_well, data_points_per_well)
        
        # Create synthetic log data with realistic patterns
        gr = 50 + 30 * np.sin(depths / 50) + np.random.normal(0, 5, data_points_per_well)
        rhob = 2.3 + 0.2 * np.sin(depths / 30) + np.random.normal(0, 0.05, data_points_per_well)
        nphi = 0.15 + 0.1 * np.cos(depths / 40) + np.random.normal(0, 0.02, data_points_per_well)
        dt = 80 + 20 * np.sin(depths / 60) + np.random.normal(0, 3, data_points_per_well)
        
        # Add some missing values to simulate real data
        missing_indices = np.random.choice(data_points_per_well, size=int(0.1 * data_points_per_well), replace=False)
        dt[missing_indices] = np.nan
        
        well_data = pd.DataFrame({
            'WELL': well,
            'MD': depths,
            'GR': gr,
            'RHOB': rhob,
            'NPHI': nphi,
            'DT': dt
        })
        
        all_data.append(well_data)
    
    df = pd.concat(all_data, ignore_index=True)
    print(f"✅ Created synthetic data: {len(df)} points, {df['WELL'].nunique()} wells")
    return df

def test_model_registry():
    """Test the enhanced MODEL_REGISTRY structure."""
    print("\n🧪 Testing MODEL_REGISTRY structure...")
    
    try:
        from ml_core import MODEL_REGISTRY, detect_device
        
        print(f"📊 Available models: {len(MODEL_REGISTRY)}")
        
        # Test model types
        shallow_models = [k for k, v in MODEL_REGISTRY.items() if v['type'] == 'shallow']
        deep_models = [k for k, v in MODEL_REGISTRY.items() if v['type'] == 'deep']
        classical_models = [k for k, v in MODEL_REGISTRY.items() if v['type'] == 'classical']
        
        print(f"   • Shallow models: {shallow_models}")
        print(f"   • Deep models: {deep_models}")
        print(f"   • Classical models: {classical_models}")
        
        # Test device detection
        device = detect_device()
        print(f"   • Detected device: {device}")
        
        # Test model metadata structure
        for model_name, model_info in list(MODEL_REGISTRY.items())[:2]:  # Test first 2 models
            required_keys = ['name', 'model_class', 'type', 'hyperparameters', 'fixed_params']
            missing_keys = [key for key in required_keys if key not in model_info]
            
            if missing_keys:
                print(f"❌ Model {model_name} missing keys: {missing_keys}")
            else:
                print(f"✅ Model {model_name} structure valid")
        
        print("✅ MODEL_REGISTRY test passed")
        return True
        
    except Exception as e:
        print(f"❌ MODEL_REGISTRY test failed: {e}")
        return False

def test_data_preprocessing():
    """Test the advanced data preprocessing pipeline."""
    print("\n🧪 Testing data preprocessing pipeline...")
    
    try:
        from data_handler import (
            create_scaling_pipeline, 
            create_sequences, 
            handle_missing_values,
            validate_data_quality,
            handle_sequence_edge_cases
        )
        
        # Create test data
        df = create_synthetic_test_data()
        feature_cols = ['GR', 'RHOB', 'NPHI']
        target_col = 'DT'
        
        # Test scaling pipeline
        print("🔄 Testing scaling pipeline...")
        scaled_df, scalers = create_scaling_pipeline(df, feature_cols, target_col, strategy='standard')
        
        if len(scalers) == len(feature_cols) + 1:  # +1 for target
            print("✅ Scaling pipeline test passed")
        else:
            print(f"❌ Scaling pipeline test failed: expected {len(feature_cols) + 1} scalers, got {len(scalers)}")
        
        # Test sequence creation
        print("🔄 Testing sequence creation...")
        sequences, targets, metadata = create_sequences(
            scaled_df, 'WELL', feature_cols, target_col, sequence_length=32, stride=16
        )
        
        if len(sequences) > 0 and len(metadata) == len(sequences):
            print(f"✅ Sequence creation test passed: {len(sequences)} sequences created")
        else:
            print("❌ Sequence creation test failed")
        
        # Test missing value handling
        print("🔄 Testing missing value handling...")
        handled_df = handle_missing_values(df, strategy='interpolate')
        original_missing = df.isnull().sum().sum()
        final_missing = handled_df.isnull().sum().sum()
        
        if final_missing <= original_missing:
            print("✅ Missing value handling test passed")
        else:
            print("❌ Missing value handling test failed")
        
        # Test data quality validation
        print("🔄 Testing data quality validation...")
        warnings_list = validate_data_quality(df, feature_cols, target_col)
        print(f"✅ Data quality validation test passed: {len(warnings_list)} warnings")
        
        # Test edge case handling
        print("🔄 Testing edge case handling...")
        short_wells, recommendation = handle_sequence_edge_cases(df, sequence_length=500)  # Intentionally large
        print(f"✅ Edge case handling test passed: {len(short_wells)} short wells detected")
        
        print("✅ Data preprocessing pipeline tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Data preprocessing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_integration():
    """Test model integration and workflow."""
    print("\n🧪 Testing model integration...")
    
    try:
        from ml_core import get_model, get_models_by_type, impute_logs
        
        # Test model instantiation
        print("🔄 Testing model instantiation...")
        
        # Test shallow model
        shallow_models = get_models_by_type('shallow')
        if shallow_models:
            model_name = list(shallow_models.keys())[0]
            model_config = {'n_features': 3}
            model = get_model(model_name, model_config)
            
            if model is not None:
                print(f"✅ Shallow model ({model_name}) instantiation test passed")
            else:
                print(f"❌ Shallow model ({model_name}) instantiation test failed")
        
        # Test deep model
        deep_models = get_models_by_type('deep')
        if deep_models:
            model_name = list(deep_models.keys())[0]
            model_config = {'n_features': 3, 'n_steps': 32, 'epochs': 1, 'batch_size': 16}
            model = get_model(model_name, model_config)
            
            if model is not None:
                print(f"✅ Deep model ({model_name}) instantiation test passed")
            else:
                print(f"❌ Deep model ({model_name}) instantiation test failed")
        
        print("✅ Model integration tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Model integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting refactored implementation tests...")
    print("=" * 60)
    
    tests = [
        ("MODEL_REGISTRY Structure", test_model_registry),
        ("Data Preprocessing Pipeline", test_data_preprocessing),
        ("Model Integration", test_model_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'=' * 20} {test_name} {'=' * 20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Refactoring implementation is working correctly.")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
