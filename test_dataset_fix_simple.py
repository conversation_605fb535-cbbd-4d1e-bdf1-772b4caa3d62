#!/usr/bin/env python3
"""
Simple test to verify that the dataset path fix allows main.py to start properly.
"""

import sys
import os

def test_configuration_parsing():
    """Test that the configuration can be parsed with the new defaults."""
    print("🔍 Testing configuration parsing with new defaults...")
    
    try:
        # Reset sys.argv to test default configuration
        original_argv = sys.argv.copy()
        sys.argv = ['test_script']
        
        from cfg import Configs
        cfg = Configs()
        args = cfg.parse_args()
        
        print(f"✅ Configuration parsed successfully!")
        print(f"   Dataset directory: {args.dataset_dir}")
        print(f"   Dataset name: {args.dataset_name}")
        print(f"   Number of folds: {args.n_folds}")
        print(f"   Logs: {args.logs}")
        
        # Restore original argv
        sys.argv = original_argv
        return True
        
    except SystemExit as e:
        print(f"❌ Configuration parsing failed with exit: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration parsing failed with error: {e}")
        return False

def test_data_loading():
    """Test that data can be loaded with the new configuration."""
    print("\n🔍 Testing data loading...")
    
    try:
        from main import loading_data
        
        # Test loading data with the new defaults
        dataset_name = "real_las_dataset"
        data_dir = "bkp_data"
        fold = 0
        
        data_train, data_val = loading_data(dataset_name, data_dir, fold)
        
        print(f"✅ Data loaded successfully!")
        print(f"   Training data shape: {data_train.shape}")
        print(f"   Validation data shape: {data_val.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return False

def test_model_instantiation():
    """Test that the LOCF model can be instantiated with the fix."""
    print("\n🔍 Testing LOCF model instantiation...")
    
    try:
        from models import Factory
        from pypots.optim import Adam
        
        # Create a factory with LOCF model
        factory = Factory(
            model='locf',
            seq_len=128,
            n_features=4,
            batch_size=32,
            epochs=1,
            patience=1,
            optimizer=Adam(lr=0.001),
            device='cpu',
            output_dir='test_output'
        )
        
        # Try to instantiate the model
        model = factory.instantiate()
        
        print(f"✅ LOCF model instantiated successfully!")
        print(f"   Model type: {type(model)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model instantiation failed: {e}")
        return False

def test_main_imports():
    """Test that main.py can be imported without errors."""
    print("\n🔍 Testing main.py imports...")
    
    try:
        # Import main functions
        from main import main, run_experiments, loading_data, get_dataset_dict
        
        print(f"✅ main.py imported successfully!")
        print(f"   Available functions: main, run_experiments, loading_data, get_dataset_dict")
        
        return True
        
    except Exception as e:
        print(f"❌ main.py import failed: {e}")
        return False

def check_dataset_files():
    """Check that the expected dataset files exist."""
    print("\n📁 Checking dataset files...")
    
    try:
        import os
        from pathlib import Path
        
        data_dir = Path("bkp_data")
        if not data_dir.exists():
            print(f"❌ Data directory {data_dir} does not exist")
            return False
        
        # Check for real_las_dataset files
        expected_files = []
        for fold in range(3):  # 0, 1, 2
            train_file = data_dir / f"real_las_dataset_fold_{fold}_well_log_sliced_train.npy"
            val_file = data_dir / f"real_las_dataset_fold_{fold}_well_log_sliced_val.npy"
            expected_files.extend([train_file, val_file])
        
        missing_files = [f for f in expected_files if not f.exists()]
        
        if missing_files:
            print(f"❌ Missing {len(missing_files)} files:")
            for f in missing_files:
                print(f"   - {f}")
            return False
        
        print(f"✅ All expected dataset files found ({len(expected_files)} files)")
        return True
        
    except Exception as e:
        print(f"❌ Error checking dataset files: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing dataset path fix (simple validation)...\n")
    
    tests = [
        ("Dataset Files Check", check_dataset_files),
        ("Main Imports", test_main_imports),
        ("Configuration Parsing", test_configuration_parsing),
        ("Data Loading", test_data_loading),
        ("Model Instantiation", test_model_instantiation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"Running {test_name}")
        print(f"{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print(f"{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! The dataset path fix is working correctly.")
        print("✅ main.py can now find datasets and start properly.")
        print("✅ The dataset path error has been resolved.")
        print("✅ LOCF model instantiation has been fixed.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print(f"{'='*60}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
