import numpy as np
import pandas as pd
from pypots.imputation.base import BaseImputer

class LOCF(BaseImputer):
    """
    Last Observation Carried Forward (LOCF).
    A simple imputation method that fills missing values with the last observed value.
    This implementation handles 3D data (n_samples, n_steps, n_features).
    """
    def __init__(self, device=None):
        super().__init__(device)

    def fit(self, train_set: dict, val_set: dict = None, file_type: str = 'h5py') -> None:
        """
        The fit method does nothing for LOCF as it's a non-trainable model.
        """
        # LOCF does not require training.
        pass

    def predict(self, test_set: dict, file_type: str = 'h5py') -> dict:
        """
        Performs imputation on the given dataset using last observation carried forward.

        Args:
            test_set (dict): A dictionary containing the dataset for testing.
                             It should have a key 'X' holding the data array with NaNs.

        Returns:
            dict: A dictionary containing the imputed data, with the key 'imputation'.
        """
        X = test_set['X']
        
        if X.ndim != 3:
            raise ValueError(f"Input data must be a 3D array, but got shape {X.shape}")

        imputed_data = np.copy(X)
        
        # Iterate over each sample in the batch
        for i in range(imputed_data.shape[0]):
            # Convert the 2D sample (n_steps, n_features) to a DataFrame
            sample_df = pd.DataFrame(imputed_data[i])
            # Apply forward fill
            sample_df.fillna(method='ffill', inplace=True)
            # Apply backward fill to handle any NaNs at the beginning of a sequence
            sample_df.fillna(method='bfill', inplace=True)
            imputed_data[i] = sample_df.values
        
        # Ensure no NaNs are left. If a whole column is NaN, bfill won't work.
        # In that case, we can fill with 0.
        if np.isnan(imputed_data).any():
            imputed_data = np.nan_to_num(imputed_data)

        return {"imputation": imputed_data}
