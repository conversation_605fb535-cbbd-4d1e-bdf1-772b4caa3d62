

training model: saits...


training model: saits...


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..


training model: saits...

training in fold 0...
training model in training set and tracking performance in validation set..
final training time in fold 0: 12m 2s
calculate validation set imputation...
mode single.1...
masking single.1 testing mean absolute error:0.1385
masking single.1 testing mean squared error:0.0392
masking single.1 testing root-mean squared error:0.1979
masking single.1 testing r2:0.9455
masking single.1 testing correlation:0.0268
mode block.20...
masking block.20 testing mean absolute error:0.2293
masking block.20 testing mean squared error:0.1186
masking block.20 testing root-mean squared error:0.3443
masking block.20 testing r2:0.8557
masking block.20 testing correlation:0.0709
mode block.100...
masking block.100 testing mean absolute error:0.3105
masking block.100 testing mean squared error:0.1890
masking block.100 testing root-mean squared error:0.4347
masking block.100 testing r2:0.6801
masking block.100 testing correlation:0.1636
mode profile...
masking profile testing mean absolute error:0.6923
masking profile testing mean squared error:0.6741
masking profile testing root-mean squared error:0.8211
masking profile testing r2:-0.0044
masking profile testing correlation:0.8864

training in fold 1...
training model in training set and tracking performance in validation set..
final training time in fold 1: 7m 35s
calculate validation set imputation...
mode single.1...
masking single.1 testing mean absolute error:0.2000
masking single.1 testing mean squared error:0.0817
masking single.1 testing root-mean squared error:0.2857
masking single.1 testing r2:0.8795
masking single.1 testing correlation:0.0589
mode block.20...
masking block.20 testing mean absolute error:0.2403
masking block.20 testing mean squared error:0.1132
masking block.20 testing root-mean squared error:0.3365
masking block.20 testing r2:0.8845
masking block.20 testing correlation:0.0595
mode block.100...
masking block.100 testing mean absolute error:0.3123
masking block.100 testing mean squared error:0.2143
masking block.100 testing root-mean squared error:0.4629
masking block.100 testing r2:0.6996
masking block.100 testing correlation:0.1602
mode profile...
masking profile testing mean absolute error:0.7959
masking profile testing mean squared error:0.9325
masking profile testing root-mean squared error:0.9657
masking profile testing r2:-0.0343
masking profile testing correlation:1.0332

training in fold 2...
training model in training set and tracking performance in validation set..
final training time in fold 2: 20m 2s
calculate validation set imputation...
mode single.1...
masking single.1 testing mean absolute error:0.2516
masking single.1 testing mean squared error:0.1251
masking single.1 testing root-mean squared error:0.3537
masking single.1 testing r2:0.8179
masking single.1 testing correlation:0.0793
mode block.20...
masking block.20 testing mean absolute error:0.2566
masking block.20 testing mean squared error:0.1418
masking block.20 testing root-mean squared error:0.3766
masking block.20 testing r2:0.8372
masking block.20 testing correlation:0.0825
mode block.100...
masking block.100 testing mean absolute error:0.2680
masking block.100 testing mean squared error:0.1548
masking block.100 testing root-mean squared error:0.3934
masking block.100 testing r2:0.8044
masking block.100 testing correlation:0.0920
mode profile...
masking profile testing mean absolute error:0.7264
masking profile testing mean squared error:0.7329
masking profile testing root-mean squared error:0.8561
masking profile testing r2:0.1226
masking profile testing correlation:0.5533
final training time: 39m 43s
----------------------------------------------


training-time: 722.0000	455.0000	1202.0000
validation-mae-single.1: 0.1385	0.2000	0.2516
validation-mse-single.1: 0.0392	0.0817	0.1251
validation-rmse-single.1: 0.1979	0.2857	0.3537
validation-r2-single.1: 0.9455	0.8795	0.8179
validation-cc-single.1: 0.0268	0.0589	0.0793
validation-mae-block.20: 0.2293	0.2403	0.2566
validation-mse-block.20: 0.1186	0.1132	0.1418
validation-rmse-block.20: 0.3443	0.3365	0.3766
validation-r2-block.20: 0.8557	0.8845	0.8372
validation-cc-block.20: 0.0709	0.0595	0.0825
validation-mae-block.100: 0.3105	0.3123	0.2680
validation-mse-block.100: 0.1890	0.2143	0.1548
validation-rmse-block.100: 0.4347	0.4629	0.3934
validation-r2-block.100: 0.6801	0.6996	0.8044
validation-cc-block.100: 0.1636	0.1602	0.0920
validation-mae-profile: 0.6923	0.7959	0.7264
validation-mse-profile: 0.6741	0.9325	0.7329
validation-rmse-profile: 0.8211	0.9657	0.8561
validation-r2-profile: -0.0044	-0.0343	0.1226
validation-cc-profile: 0.8864	1.0332	0.5533


training model: saits...


training model: saits...

training in fold 0...


training model: saits...

training in fold 0...
