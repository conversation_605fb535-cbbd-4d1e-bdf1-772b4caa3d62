# Real LAS Dataset for Well Log Imputation Benchmark

This directory contains tools to create and test datasets from real LAS files for the well log imputation benchmark system.

## Files

- `create_test_dataset.py` - <PERSON>ript to generate datasets from real LAS files
- `test_real_dataset.py` - <PERSON><PERSON><PERSON> to test the generated real datasets
- `test_dataset_usage.py` - <PERSON><PERSON><PERSON> to test synthetic datasets (legacy)
- `README.md` - This documentation file
- `*.las` - Real well log LAS files (9 wells included)

## Quick Start

### 1. Generate a Dataset from Real LAS Files

```bash
# Generate dataset from LAS files in bkp_data folder
python bkp_data/create_test_dataset.py --dataset_name real_las_dataset

# Generate with custom parameters
python bkp_data/create_test_dataset.py \
    --dataset_name my_real_dataset \
    --las_folder bkp_data \
    --n_folds 5 \
    --seq_len 256 \
    --slice_stride 128 \
    --logs GR DT RHOB NPHI
```

### 2. Test the Generated Real Dataset

```bash
# Test the real dataset
python bkp_data/test_real_dataset.py --dataset_name real_las_dataset

# Test a specific dataset and fold
python bkp_data/test_real_dataset.py \
    --dataset_name my_real_dataset \
    --fold 0 \
    --n_samples 5
```

### 3. Use with the Main Benchmark System

```bash
# Run imputation benchmark with the real dataset
python main.py \
    --dataset_name real_las_dataset \
    --dataset_dir bkp_data \
    --model saits \
    --epochs 10 \
    --batch_size 16
```

## Generated Dataset Structure

The script creates a complete dataset structure compatible with the benchmark system:

```
bkp_data/
├── real_las_dataset_fold_0_well_log_sliced_train.npy          # Training data for fold 0
├── real_las_dataset_fold_0_well_log_sliced_val.npy            # Validation data for fold 0
├── real_las_dataset_fold_0_well_log_metadata_train.json       # Training metadata for fold 0
├── real_las_dataset_fold_0_well_log_metadata_val.json         # Validation metadata for fold 0
├── real_las_dataset_fold_0_well_log_slices_meta_train.json    # Training slices metadata for fold 0
├── real_las_dataset_fold_0_well_log_slices_meta_val.json      # Validation slices metadata for fold 0
├── ... (similar files for other folds)
```

## Available LAS Files

The directory contains 9 real well log files:
- B-G-10_RP_INPUT.las
- B-G-6_RP_INPUT.las
- B-L-14_RP_INPUT.las
- B-L-15_RP_INPUT.las
- B-L-1_RP_INPUT.las
- B-L-2.G1_RP_INPUT.las
- B-L-6_RP_INPUT.las
- B-L-9_RP_INPUT.las
- EB-1_RP_INPUT.las

## Dataset Features

The real dataset includes four standard well log features extracted from LAS files:

1. **GR** (Gamma Ray) - Natural radioactivity measurement
2. **DT** (Delta Time Compressional) - P-wave slowness
3. **RHOB** (Bulk Density) - Formation bulk density
4. **NPHI** (Neutron Porosity) - Neutron porosity measurement

## Data Processing Pipeline

1. **LAS File Loading**: Reads all .las files from the specified folder
2. **Data Cleaning**: Handles missing values and standardizes column names
3. **Outlier Removal**: Applies winsorization (clips 1st and 99th percentiles)
4. **Normalization**: Standardizes features using StandardScaler
5. **Slicing**: Creates fixed-length sequences with sliding window
6. **Cross-validation**: Splits data into training/validation folds

## Data Characteristics

- **Real geological data**: Actual well log measurements from 9 wells
- **Quality control**: Outliers removed, data normalized and validated
- **No missing values**: Only complete sequences are included in final dataset
- **Format compatibility**: Data format matches exactly what the benchmark system expects

## Command Line Options

### create_test_dataset.py

- `--dataset_name`: Name of the dataset (default: "real_dataset")
- `--las_folder`: Folder containing LAS files (default: "bkp_data")
- `--seq_len`: Length of each sequence (default: 256)
- `--slice_stride`: Stride between slice starting points (default: 256)
- `--n_folds`: Number of cross-validation folds (default: 5)
- `--output_dir`: Output directory for the dataset (default: "bkp_data")
- `--logs`: Well log curves to extract (default: ['GR', 'DT', 'RHOB', 'NPHI'])
- `--seed`: Random seed for reproducibility (default: 42)

### test_real_dataset.py

- `--dataset_name`: Name of the real dataset (default: "real_dataset")
- `--dataset_dir`: Directory containing the dataset (default: "bkp_data")
- `--fold`: Fold number to test (default: 0)
- `--n_samples`: Number of samples to use for testing (default: 10)

## Example Output

When you run the dataset creation script, you should see output like:

```
Creating dataset 'real_las_dataset' from LAS files in 'bkp_data'...
Target logs: ['GR', 'DT', 'RHOB', 'NPHI']

1. Loading LAS files...
Loading B-G-10_RP_INPUT.las...
  Loaded 8737 samples from B-G-10_RP_INPUT.las
Loading B-G-6_RP_INPUT.las...
  Loaded 7248 samples from B-G-6_RP_INPUT.las
... (loading other wells)

Total combined data: 59606 samples from 9 wells

2. Preprocessing data...
  Winsorizing outliers...
  GR: clipped 1188 outliers (bounds: 14.55 - 121.11)
  DT: clipped 808 outliers (bounds: 73.28 - 167.76)
  RHOB: clipped 830 outliers (bounds: 1.63 - 2.62)
  NPHI: clipped 964 outliers (bounds: 0.10 - 0.77)
  Normalizing data...
  GR: normalized 58210 values
  DT: normalized 39577 values
  RHOB: normalized 41595 values
  NPHI: normalized 47158 values

3. Creating slices...
  Well B-G-10_RP_INPUT: created 19 slices
  Well B-G-6_RP_INPUT: created 19 slices
  ... (processing other wells)

Total slices created: 141 with shape (141, 128, 4)

4. Creating 3 folds...
Processing fold 0...
  Fold 0: 94 train, 47 val samples
... (processing other folds)

✅ Dataset creation complete!
```

When you run the test script, you should see output like:

```
Testing real dataset: real_las_dataset
Dataset directory: bkp_data
Fold: 0

📊 Dataset loaded successfully!
Training data shape: (94, 128, 4)
Validation data shape: (47, 128, 4)
Features: ['GR', 'DT', 'RHOB', 'NPHI']

🔍 Training data analysis:
  Data shape: (94, 128, 4)
  Data type: float64
  NaN values: 0 / 48128 (0.00%)
  Feature statistics:
    GR: mean=0.231, std=0.896, min=-2.368, max=2.085
    DT: mean=0.000, std=0.954, min=-1.424, max=2.693
    RHOB: mean=0.063, std=0.920, min=-4.274, max=1.470
    NPHI: mean=0.029, std=0.864, min=-1.818, max=4.766

✅ All tests passed!
The real dataset is compatible with the imputation benchmark system.

📋 Usage with main system:
python main.py --dataset_name real_las_dataset --dataset_dir bkp_data --model saits --epochs 10
```

## Notes

- **Real geological data**: This dataset uses actual well log measurements from 9 wells
- **Production ready**: Suitable for real imputation experiments and research
- **Quality assured**: Data has been cleaned, normalized, and validated
- **Benchmark compatible**: Generated files follow the exact naming convention expected by the benchmark system
- **Flexible processing**: Configurable slice length, stride, and feature selection
- **Cross-validation ready**: Proper fold splits for robust model evaluation
