

training model: locf...

training in fold 0...
training model in training set and tracking performance in validation set..
final training time in fold 0: 0m 0s
calculate validation set imputation...
mode single.1...
masking single.1 testing mean absolute error:0.2045
masking single.1 testing mean squared error:0.0860
masking single.1 testing root-mean squared error:0.2933
masking single.1 testing r2:0.8804
masking single.1 testing correlation:0.0593
mode block.20...
masking block.20 testing mean absolute error:0.4125
masking block.20 testing mean squared error:0.4612
masking block.20 testing root-mean squared error:0.6791
masking block.20 testing r2:0.4385
masking block.20 testing correlation:0.3036
mode block.100...
masking block.100 testing mean absolute error:0.4761
masking block.100 testing mean squared error:0.4640
masking block.100 testing root-mean squared error:0.6812
masking block.100 testing r2:0.2147
masking block.100 testing correlation:0.4518
mode profile...
masking profile testing mean absolute error:0.6992
masking profile testing mean squared error:0.6841
masking profile testing root-mean squared error:0.8271
masking profile testing r2:-0.0192
masking profile testing correlation:nan
final training time: 0m 0s
----------------------------------------------


training-time: 0.0000
validation-mae-single.1: 0.2045
validation-mse-single.1: 0.0860
validation-rmse-single.1: 0.2933
validation-r2-single.1: 0.8804
validation-cc-single.1: 0.0593
validation-mae-block.20: 0.4125
validation-mse-block.20: 0.4612
validation-rmse-block.20: 0.6791
validation-r2-block.20: 0.4385
validation-cc-block.20: 0.3036
validation-mae-block.100: 0.4761
validation-mse-block.100: 0.4640
validation-rmse-block.100: 0.6812
validation-r2-block.100: 0.2147
validation-cc-block.100: 0.4518
validation-mae-profile: 0.6992
validation-mse-profile: 0.6841
validation-rmse-profile: 0.8271
validation-r2-profile: -0.0192
validation-cc-profile: nan


training model: locf...
