import numpy as np

def masked_fill(data, mask, fill_value):
    """
    Fill values in data array where mask is True with fill_value.

    Args:
        data: Input array to be filled
        mask: Boolean mask indicating where to fill values (can be float or boolean)
        fill_value: Value to fill where mask is True

    Returns:
        Array with values filled according to mask
    """
    result = data.copy()
    # Convert mask to boolean if it's not already boolean
    # This handles cases where mask is float32 (like from missing_mask operations)
    if mask.dtype != bool:
        boolean_mask = mask.astype(bool)
    else:
        boolean_mask = mask
    result[boolean_mask] = fill_value
    return result

# Test the function
data = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
mask = np.array([[True, False, True], [False, True, False]])
result = masked_fill(data, mask, np.nan)

print("Test successful!")
print("Data:", data)
print("Mask:", mask)
print("Result:", result)
