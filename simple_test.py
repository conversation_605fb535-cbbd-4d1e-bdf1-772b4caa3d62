import numpy as np

def masked_fill(data, mask, fill_value):
    """
    Fill values in data array where mask is True with fill_value.
    """
    result = data.copy()
    result[mask] = fill_value
    return result

# Test the function
data = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
mask = np.array([[True, False, True], [False, True, False]])
result = masked_fill(data, mask, np.nan)

print("Test successful!")
print("Data:", data)
print("Mask:", mask)
print("Result:", result)
