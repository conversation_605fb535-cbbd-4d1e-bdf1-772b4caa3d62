'''
This module handles the generation of reports and visualizations.
'''

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from typing import List, Dict, Any
from fpdf import FPDF

def plot_well_logs(original_data: np.ndarray, imputed_data: np.ndarray, missing_mask: np.ndarray, 
                   log_names: List[str], well_id: str, output_path: str):
    """
    Generates and saves a plot comparing original, imputed, and missing data for a single well.

    Args:
        original_data: The original well log data (including missing values).
        imputed_data: The well log data after imputation.
        missing_mask: A boolean mask indicating the locations of missing values.
        log_names: A list of names for the logs being plotted.
        well_id: The identifier for the well.
        output_path: The path to save the generated plot image.
    """
    num_logs = original_data.shape[1]
    fig, axes = plt.subplots(1, num_logs, figsize=(5 * num_logs, 10), sharey=True)
    fig.suptitle(f'Well: {well_id} - Imputation Results', fontsize=16)

    depth = np.arange(original_data.shape[0])

    for i in range(num_logs):
        ax = axes[i]
        # Plot original data where it exists
        ax.plot(original_data[:, i], depth, label='Original Data', color='black', lw=1)
        # Overlay imputed data where original was missing
        ax.plot(np.where(missing_mask[:, i], imputed_data[:, i], np.nan), depth, label='Imputed Data', color='red', lw=1, linestyle='--')
        ax.set_title(log_names[i])
        ax.set_xlabel('Value')
        if i == 0:
            ax.set_ylabel('Depth/Index')
        ax.invert_yaxis()
        ax.grid(True, which='both', linestyle='--', linewidth=0.5)
        ax.legend()

    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(output_path)
    plt.close()

class PDFReport(FPDF):
    def header(self):
        self.set_font('Arial', 'B', 12)
        self.cell(0, 10, 'Well Log Imputation Report', 0, 1, 'C')

    def footer(self):
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')

    def chapter_title(self, title):
        self.set_font('Arial', 'B', 12)
        self.cell(0, 10, title, 0, 1, 'L')
        self.ln(10)

    def chapter_body(self, body):
        self.set_font('Arial', '', 12)
        self.multi_cell(0, 10, body)
        self.ln()

    def add_plot(self, plot_path, width=190):
        self.image(plot_path, x=10, w=width)
        self.ln()

def generate_report(config: Dict[str, Any], well_plots: List[str], output_path: str):
    """
    Generates a PDF report summarizing the imputation process.

    Args:
        config: The configuration dictionary used for the run.
        well_plots: A list of file paths to the generated well plots.
        output_path: The path to save the final PDF report.
    """
    pdf = PDFReport()
    pdf.add_page()

    # Add summary section
    pdf.chapter_title('Configuration Summary')
    config_summary = (
        f"Model Used: {config.get('model_settings', {}).get('name', 'N/A')}\n"
        f"Logs Processed: {', '.join(config.get('data_settings', {}).get('selected_logs', []))}\n"
        f"Data Source: {config.get('data_settings', {}).get('las_folder', 'N/A')}"
    )
    pdf.chapter_body(config_summary)

    # Add plots section
    pdf.chapter_title('Well Imputation Plots')
    for plot_path in well_plots:
        pdf.add_plot(plot_path)

    pdf.output(output_path, 'F')
    print(f"Report generated and saved to {output_path}")
