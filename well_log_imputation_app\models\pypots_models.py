'''
This module contains wrappers for advanced imputation models from the PyPOTS library.
'''

from pypots.imputation import <PERSON>IT<PERSON>, Transformer, BRITS, MRNN

# The PyPOTS models already follow a scikit-learn-like API with fit/predict methods.
# These wrapper classes are included for consistency and to allow for any future
# parameter mapping or custom logic without changing the core ML workflow.

class SAITSImputer(SAITS):
    def __init__(self, n_steps: int, n_features: int, n_layers: int, d_model: int, d_inner: int, n_head: int, d_k: int, d_v: int, dropout: float, epochs: int, patience: int, batch_size: int, device=None):
        super().__init__(n_steps=n_steps, n_features=n_features, n_layers=n_layers, d_model=d_model, d_inner=d_inner, n_head=n_head, d_k=d_k, d_v=d_v, dropout=dropout, epochs=epochs, patience=patience, batch_size=batch_size, device=device)

class TransformerImputer(Transformer):
    def __init__(self, n_steps: int, n_features: int, n_layers: int, d_model: int, d_inner: int, n_head: int, d_k: int, d_v: int, dropout: float, epochs: int, patience: int, batch_size: int, device=None):
        super().__init__(n_steps=n_steps, n_features=n_features, n_layers=n_layers, d_model=d_model, d_inner=d_inner, n_head=n_head, d_k=d_k, d_v=d_v, dropout=dropout, epochs=epochs, patience=patience, batch_size=batch_size, device=device)

class BRITSImputer(BRITS):
    def __init__(self, n_steps: int, n_features: int, rnn_hidden_size: int, epochs: int, patience: int, batch_size: int, device=None):
        super().__init__(n_steps=n_steps, n_features=n_features, rnn_hidden_size=rnn_hidden_size, epochs=epochs, patience=patience, batch_size=batch_size, device=device)

# Placeholder classes for other models to be added.

class MRNNImputer(MRNN):
    def __init__(self, n_steps: int, n_features: int, rnn_hidden_size: int, epochs: int, patience: int, batch_size: int, device=None):
        super().__init__(n_steps=n_steps, n_features=n_features, rnn_hidden_size=rnn_hidden_size, epochs=epochs, patience=patience, batch_size=batch_size, device=device)
