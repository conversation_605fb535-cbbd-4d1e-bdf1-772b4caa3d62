# IndexError Fix Documentation

## Problem Description

The `main.py` script was failing with an IndexError at line 32 in the `masked_fill` function:

```
IndexError: arrays used as indices must be of integer (or boolean) type
```

This error occurred when the function tried to use a mask array for indexing, but the mask array was not of the correct boolean type.

## Root Cause Analysis

1. **Source of the Issue**: In the `mask_X` function in `data/missing.py` at line 127:
   ```python
   missing_mask = (~np.isnan(X)).astype(np.float32)
   ```
   The `missing_mask` is explicitly converted to `np.float32` type.

2. **Problematic Operation**: In `main.py` at line 118:
   ```python
   X = masked_fill(X, 1 - missing_mask, np.nan)
   ```
   The expression `1 - missing_mask` results in a `float32` array, not a boolean array.

3. **Indexing Error**: In the original `masked_fill` function at line 32:
   ```python
   result[mask] = fill_value
   ```
   NumPy requires boolean or integer arrays for indexing, but received a `float32` array.

## Solution

Modified the `masked_fill` function in `main.py` to automatically convert non-boolean masks to boolean type before using them for array indexing:

```python
def masked_fill(data, mask, fill_value):
    """
    Fill values in data array where mask is True with fill_value.

    This function replaces the deprecated pypots.data.masked_fill function.

    Args:
        data: Input array to be filled
        mask: Boolean mask indicating where to fill values (can be float or boolean)
        fill_value: Value to fill where mask is True

    Returns:
        Array with values filled according to mask
    """
    result = data.copy()
    # Convert mask to boolean if it's not already boolean
    # This handles cases where mask is float32 (like from missing_mask operations)
    if mask.dtype != bool:
        boolean_mask = mask.astype(bool)
    else:
        boolean_mask = mask
    result[boolean_mask] = fill_value
    return result
```

## Key Changes

1. **Type Check**: Added `if mask.dtype != bool:` to check if the mask is already boolean
2. **Automatic Conversion**: Convert non-boolean masks to boolean using `mask.astype(bool)`
3. **Backward Compatibility**: The function still works with existing boolean masks
4. **Documentation Update**: Updated the docstring to indicate that the mask can be float or boolean

## Files Modified

- `main.py`: Fixed the `masked_fill` function (lines 17-39)
- `simple_test.py`: Updated to match the fixed version
- `test_import_fix.py`: Updated to match the fixed version

## Testing

The fix was thoroughly tested with:

1. **Boolean masks**: Verified existing functionality still works
2. **Float32 masks**: Tested the specific problematic scenario
3. **Real data scenarios**: Used actual `missing_mask` data from `mask_X` function
4. **Edge cases**: Different data types and sizes
5. **Integration test**: Full `get_dataset_dict` function with `fill=True`

All tests passed successfully, confirming that:
- The IndexError is resolved
- The intended functionality is preserved
- The fix handles both boolean and float masks correctly

## Impact

This fix allows the well log imputation experiments to run successfully through the `main.py` script without array indexing type errors. The `get_dataset_dict` function can now be called with `fill=True` without causing an IndexError.

## No Side Effects

The fix is designed to be backward compatible and doesn't affect:
- Other parts of the codebase that use boolean masks
- The shallow models in `models/shallow.py` (they use boolean comparisons)
- Any existing functionality that was working correctly

The fix only affects the specific case where a float32 mask is passed to the `masked_fill` function, which was the source of the IndexError.
