#!/usr/bin/env python3
"""
Test script to verify that main.py imports work correctly after the fix.
"""

try:
    # Test importing the main functions
    from main import masked_fill, select_optimizer, loading_data, get_dataset_dict
    print("✓ Successfully imported functions from main.py")
    
    # Test the masked_fill function specifically
    import numpy as np
    
    # Test basic functionality
    data = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
    mask = np.array([[True, False, True], [False, True, False]])
    result = masked_fill(data, mask, np.nan)
    
    print("✓ masked_fill function works correctly")
    print(f"Input data: {data}")
    print(f"Mask: {mask}")
    print(f"Result: {result}")
    
    # Test the specific usage pattern from main.py
    X = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
    missing_mask = np.array([[False, True, False], [True, False, True]])
    
    # This is how it's used in main.py: masked_fill(X, 1 - missing_mask, np.nan)
    result = masked_fill(X, 1 - missing_mask, np.nan)
    print("✓ masked_fill works with the main.py usage pattern")
    print(f"X: {X}")
    print(f"missing_mask: {missing_mask}")
    print(f"1 - missing_mask: {1 - missing_mask}")
    print(f"Result: {result}")
    
    print("\n🎉 All tests passed! The ImportError has been fixed.")
    
except ImportError as e:
    print(f"❌ ImportError still exists: {e}")
except Exception as e:
    print(f"❌ Other error occurred: {e}")
