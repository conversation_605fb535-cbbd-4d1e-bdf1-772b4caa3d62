#!/usr/bin/env python3
"""
Comprehensive test script to verify that all ImportErrors in main.py have been fixed.
"""

import numpy as np

def test_all_imports():
    """Test all the imports that were causing issues."""
    print("🔍 Testing all PyPOTS imports that were causing issues...")
    
    try:
        # Test the main metrics import (the primary fix)
        from pypots.nn.functional import calc_mae, calc_mse, calc_rmse
        print("✓ Successfully imported calc_mae, calc_mse, calc_rmse from pypots.nn.functional")
        
        # Test the BaseDataset import (secondary fix)
        from pypots.data import BaseDataset
        print("✓ Successfully imported BaseDataset from pypots.data")
        
        # Test other PyPOTS imports used in the codebase
        from pypots.optim import Adam, AdamW
        from pypots.utils.random import set_random_seed
        from pypots.imputation.base import BaseNNImputer
        from pypots.utils.logging import logger
        print("✓ Successfully imported other PyPOTS components")
        
        return True
        
    except ImportError as e:
        print(f"❌ ImportError still exists: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error occurred: {e}")
        return False

def test_metric_functions():
    """Test that the metric functions work correctly."""
    print("\n🧮 Testing metric function functionality...")
    
    try:
        from pypots.nn.functional import calc_mae, calc_mse, calc_rmse
        
        # Create test data similar to what would be used in main.py
        predictions = np.array([
            [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]],
            [[1.1, 2.1, 2.9], [3.9, 5.1, 6.1]]
        ])
        targets = np.array([
            [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]],
            [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]
        ])
        mask = np.array([
            [[True, True, True], [True, True, True]],
            [[True, False, True], [False, True, True]]
        ])
        
        # Test the functions
        mae = calc_mae(predictions, targets, mask)
        mse = calc_mse(predictions, targets, mask)
        rmse = calc_rmse(predictions, targets, mask)
        
        print(f"✓ calc_mae result: {mae:.6f}")
        print(f"✓ calc_mse result: {mse:.6f}")
        print(f"✓ calc_rmse result: {rmse:.6f}")
        
        # Verify the results make sense
        if mae >= 0 and mse >= 0 and rmse >= 0:
            print("✓ All metric functions returned non-negative values as expected")
        else:
            print("✗ Unexpected metric values")
            return False
            
        # Verify RMSE is square root of MSE (approximately)
        if abs(rmse - np.sqrt(mse)) < 1e-6:
            print("✓ RMSE equals sqrt(MSE) as expected")
        else:
            print("✗ RMSE does not equal sqrt(MSE)")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing metric functions: {e}")
        return False

def test_main_py_import():
    """Test that main.py can be imported without errors."""
    print("\n📄 Testing main.py import...")
    
    try:
        # Import main functions
        from main import masked_fill, select_optimizer, loading_data, get_dataset_dict, run_experiments, print_table
        print("✓ Successfully imported main functions from main.py")
        
        # Test the masked_fill function
        data = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
        mask = np.array([[True, False, True], [False, True, False]])
        result = masked_fill(data, mask, np.nan)
        print("✓ masked_fill function works correctly")
        
        # Test select_optimizer function
        optimizer = select_optimizer('adam', 0.001)
        print("✓ select_optimizer function works correctly")
        
        return True
        
    except ImportError as e:
        print(f"❌ ImportError in main.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error in main.py: {e}")
        return False

def test_models_import():
    """Test that the models can be imported without errors."""
    print("\n🤖 Testing models import...")
    
    try:
        # Test importing models that were fixed
        import models
        print("✓ Successfully imported models module")
        
        # Test that the Factory class can be instantiated
        from models import Factory
        print("✓ Successfully imported Factory class")
        
        return True
        
    except ImportError as e:
        print(f"❌ ImportError in models: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error in models: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Running comprehensive ImportError fix verification tests...\n")
    
    tests = [
        ("PyPOTS Imports", test_all_imports),
        ("Metric Functions", test_metric_functions),
        ("Main.py Import", test_main_py_import),
        ("Models Import", test_models_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"Running {test_name} Test")
        print(f"{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print(f"{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! The ImportError fixes are working correctly.")
        print("✅ main.py can now be imported and executed without ImportErrors.")
        print("✅ All PyPOTS metric functions are working with the correct names.")
        print("✅ All PyPOTS data imports are using the correct module paths.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print(f"{'='*60}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
