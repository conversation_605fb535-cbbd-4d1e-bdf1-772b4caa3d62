#!/usr/bin/env python3
"""
Real LAS Dataset Generator for Well Log Imputation Benchmark

This script creates datasets from actual LAS files that match the expected format
for the well log imputation benchmark system.

The generated datasets include:
- Real well log data from LAS files
- Proper numpy array format with shape (n_samples, seq_len, n_features)
- Standard well log features: GR, DT, RHOB, NPHI
- Multiple folds for cross-validation
- Associated metadata files

Usage:
    python create_test_dataset.py --dataset_name real_dataset --las_folder bkp_data --n_folds 5
"""

import numpy as np
import pandas as pd
import json
import os
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional
import lasio
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import KFold


def load_las_files(las_folder: str, selected_logs: List[str] = ['GR', 'DT', 'RHOB', 'NPHI']) -> pd.DataFrame:
    """
    Load well log data from LAS files in a folder.

    Args:
        las_folder: Path to folder containing LAS files
        selected_logs: List of log curves to extract

    Returns:
        pandas DataFrame with well log data
    """
    data_frames = []

    # Standard name mappings for common variations
    std_names = {
        'DEN': 'RHOB', 'DENS': 'RHOB',
        'NPOR': 'NPHI', 'PHIN': 'NPHI',
        'DTC': 'DT', 'DTCO': 'DT'
    }

    for las_file in os.listdir(las_folder):
        if not (las_file.endswith('.las') or las_file.endswith('.LAS')):
            continue

        las_path = os.path.join(las_folder, las_file)
        print(f"Loading {las_file}...")

        try:
            # Read LAS file
            las_obj = lasio.read(las_path)
            df = las_obj.df().reset_index()

            # Apply standard name mappings
            df = df.rename(columns=std_names)

            # Handle GR variations
            if 'GR' not in df.columns:
                if 'GRN' in df.columns:
                    df = df.rename(columns={'GRN': 'GR'})
                elif 'GRD' in df.columns:
                    df = df.rename(columns={'GRD': 'GR'})

            # Check if we have the required logs
            available_logs = [log for log in selected_logs if log in df.columns]
            if len(available_logs) < len(selected_logs):
                missing_logs = set(selected_logs) - set(available_logs)
                print(f"  Warning: Missing logs {missing_logs} in {las_file}")
                print(f"  Available logs: {list(df.columns)}")
                continue

            # Extract selected logs and add well identifier
            df_selected = df[['DEPTH'] + selected_logs].copy()
            df_selected['WELL'] = las_file.split('.')[0]

            # Remove rows with all NaN values in log columns
            df_selected = df_selected.dropna(subset=selected_logs, how='all')

            if len(df_selected) > 0:
                data_frames.append(df_selected)
                print(f"  Loaded {len(df_selected)} samples from {las_file}")
            else:
                print(f"  No valid data in {las_file}")

        except Exception as e:
            print(f"  Error loading {las_file}: {e}")
            continue

    if not data_frames:
        raise ValueError("No valid LAS files found or loaded")

    # Combine all data
    combined_data = pd.concat(data_frames, ignore_index=True)
    print(f"\nTotal combined data: {len(combined_data)} samples from {len(data_frames)} wells")

    return combined_data


def winsorize_data(data: pd.DataFrame, logs: List[str], percentiles: tuple = (1, 99)) -> pd.DataFrame:
    """
    Apply winsorization to clip outliers.

    Args:
        data: DataFrame with well log data
        logs: List of log columns to winsorize
        percentiles: Tuple of (lower, upper) percentiles for clipping

    Returns:
        DataFrame with winsorized data
    """
    data_copy = data.copy()

    for log in logs:
        if log in data_copy.columns:
            lower_bound = np.percentile(data_copy[log].dropna(), percentiles[0])
            upper_bound = np.percentile(data_copy[log].dropna(), percentiles[1])

            # Set outliers to NaN
            mask = (data_copy[log] < lower_bound) | (data_copy[log] > upper_bound)
            data_copy.loc[mask, log] = np.nan

            print(f"  {log}: clipped {mask.sum()} outliers (bounds: {lower_bound:.2f} - {upper_bound:.2f})")

    return data_copy


def normalize_data(data: pd.DataFrame, logs: List[str]) -> pd.DataFrame:
    """
    Normalize well log data using StandardScaler.

    Args:
        data: DataFrame with well log data
        logs: List of log columns to normalize

    Returns:
        DataFrame with normalized data
    """
    data_copy = data.copy()
    scaler = StandardScaler()

    for log in logs:
        if log in data_copy.columns:
            # Get non-NaN values
            valid_mask = ~data_copy[log].isna()
            if valid_mask.sum() > 0:
                # Fit and transform non-NaN values
                values = data_copy.loc[valid_mask, log].values.reshape(-1, 1)
                normalized_values = scaler.fit_transform(values).flatten()
                data_copy.loc[valid_mask, log] = normalized_values

                print(f"  {log}: normalized {valid_mask.sum()} values")

    return data_copy


def create_slices_from_wells(data: pd.DataFrame, logs: List[str],
                           slice_size: int = 256, slice_stride: int = 256) -> tuple[np.ndarray, List[Dict]]:
    """
    Create slices from well log data.

    Args:
        data: DataFrame with well log data
        logs: List of log columns to include
        slice_size: Length of each slice
        slice_stride: Stride between slice starting points

    Returns:
        Tuple of (slices_array, slices_metadata)
    """
    slices_list = []
    slices_metadata = []

    wells = data['WELL'].unique()

    for well in wells:
        well_data = data[data['WELL'] == well].copy()
        well_data = well_data.sort_values('DEPTH')

        # Extract log values
        log_values = well_data[logs].values

        # Remove rows with any NaN values
        valid_mask = ~np.isnan(log_values).any(axis=1)
        log_values = log_values[valid_mask]

        if len(log_values) < slice_size:
            print(f"  Warning: Well {well} has only {len(log_values)} valid samples, skipping")
            continue

        # Create slices
        well_slices = 0
        for start_idx in range(0, len(log_values) - slice_size + 1, slice_stride):
            end_idx = start_idx + slice_size
            slice_data = log_values[start_idx:end_idx]

            # Check if slice has any NaN values
            if not np.isnan(slice_data).any():
                slices_list.append(slice_data)
                slices_metadata.append({
                    'well': well,
                    'start_depth': well_data.iloc[start_idx]['DEPTH'],
                    'end_depth': well_data.iloc[end_idx-1]['DEPTH'],
                    'slice_index': well_slices
                })
                well_slices += 1

        print(f"  Well {well}: created {well_slices} slices")

    if not slices_list:
        raise ValueError("No valid slices could be created from the data")

    slices_array = np.array(slices_list)
    print(f"\nTotal slices created: {len(slices_array)} with shape {slices_array.shape}")

    return slices_array, slices_metadata


def create_fold_split(n_samples: int, n_folds: int, seed: int = 42) -> List[Dict[str, List[int]]]:
    """
    Create fold splits for cross-validation.
    
    Args:
        n_samples: Total number of samples
        n_folds: Number of folds
        seed: Random seed
        
    Returns:
        List of dictionaries with 'train' and 'val' indices for each fold
    """
    np.random.seed(seed)
    indices = np.random.permutation(n_samples)
    fold_size = n_samples // n_folds
    
    folds = []
    for fold in range(n_folds):
        val_start = fold * fold_size
        val_end = (fold + 1) * fold_size if fold < n_folds - 1 else n_samples
        
        val_indices = indices[val_start:val_end].tolist()
        train_indices = np.concatenate([indices[:val_start], indices[val_end:]]).tolist()
        
        folds.append({
            'train': train_indices,
            'val': val_indices
        })
    
    return folds


def create_metadata(dataset_name: str, fold: int, partition: str, 
                   n_samples: int, seq_len: int, features: List[str]) -> Dict[str, Any]:
    """
    Create metadata dictionary for a dataset partition.
    
    Args:
        dataset_name: Name of the dataset
        fold: Fold number
        partition: 'train' or 'val'
        n_samples: Number of samples in this partition
        seq_len: Sequence length
        features: List of feature names
        
    Returns:
        Metadata dictionary
    """
    return {
        "dataset_name": dataset_name,
        "fold": fold,
        "partition": partition,
        "n_samples": n_samples,
        "sequence_length": seq_len,
        "n_features": len(features),
        "feature_names": features,
        "data_shape": [n_samples, seq_len, len(features)],
        "description": f"Synthetic test dataset for well log imputation benchmark - fold {fold} {partition} partition"
    }


def create_slices_metadata(dataset_name: str, fold: int, partition: str, 
                          indices: List[int], seq_len: int) -> Dict[str, Any]:
    """
    Create per-well metadata for slices.
    
    Args:
        dataset_name: Name of the dataset
        fold: Fold number
        partition: 'train' or 'val'
        indices: Sample indices in this partition
        seq_len: Sequence length
        
    Returns:
        Slices metadata dictionary
    """
    wells_meta = {}
    for i, idx in enumerate(indices):
        well_name = f"synthetic_well_{idx:04d}"
        wells_meta[well_name] = {
            "well_id": idx,
            "n_slices": 1,  # Each sample is one slice
            "slice_length": seq_len,
            "slice_indices": [i]  # Index in the partition array
        }
    
    return {
        "dataset_name": dataset_name,
        "fold": fold,
        "partition": partition,
        "n_wells": len(indices),
        "wells": wells_meta
    }


def main():
    parser = argparse.ArgumentParser(description="Generate dataset from real LAS files for well log imputation")
    parser.add_argument("--dataset_name", type=str, default="real_dataset",
                       help="Name of the dataset")
    parser.add_argument("--las_folder", type=str, default="bkp_data",
                       help="Folder containing LAS files")
    parser.add_argument("--seq_len", type=int, default=256,
                       help="Length of each sequence")
    parser.add_argument("--slice_stride", type=int, default=256,
                       help="Stride between slice starting points")
    parser.add_argument("--n_folds", type=int, default=5,
                       help="Number of cross-validation folds")
    parser.add_argument("--output_dir", type=str, default="bkp_data",
                       help="Output directory for the dataset")
    parser.add_argument("--logs", type=str, nargs='+', default=['GR', 'DT', 'RHOB', 'NPHI'],
                       help="Well log curves to extract")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed for reproducibility")

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"Creating dataset '{args.dataset_name}' from LAS files in '{args.las_folder}'...")
    print(f"Target logs: {args.logs}")

    # Load LAS files
    print("\n1. Loading LAS files...")
    raw_data = load_las_files(args.las_folder, args.logs)

    # Preprocess data
    print("\n2. Preprocessing data...")
    print("  Winsorizing outliers...")
    processed_data = winsorize_data(raw_data, args.logs)

    print("  Normalizing data...")
    processed_data = normalize_data(processed_data, args.logs)

    # Create slices
    print("\n3. Creating slices...")
    slices_array, slices_metadata = create_slices_from_wells(
        processed_data, args.logs, args.seq_len, args.slice_stride
    )

    print(f"Final data shape: {slices_array.shape}")

    # Create fold splits based on slices
    n_samples = len(slices_array)
    folds = create_fold_split(n_samples, args.n_folds, args.seed)
    
    # Save data for each fold
    print(f"\n4. Creating {args.n_folds} folds...")
    for fold_idx, fold_data in enumerate(folds):
        print(f"Processing fold {fold_idx}...")

        # Extract train and validation data
        train_indices = fold_data['train']
        val_indices = fold_data['val']

        train_data = slices_array[train_indices]
        val_data = slices_array[val_indices]

        # Save numpy arrays
        train_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_sliced_train.npy"
        val_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_sliced_val.npy"

        np.save(train_file, train_data)
        np.save(val_file, val_data)

        # Create and save metadata
        train_metadata = create_metadata(
            args.dataset_name, fold_idx, 'train',
            len(train_indices), args.seq_len, args.logs
        )
        val_metadata = create_metadata(
            args.dataset_name, fold_idx, 'val',
            len(val_indices), args.seq_len, args.logs
        )

        train_meta_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_metadata_train.json"
        val_meta_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_metadata_val.json"

        with open(train_meta_file, 'w') as f:
            json.dump(train_metadata, f, indent=2)
        with open(val_meta_file, 'w') as f:
            json.dump(val_metadata, f, indent=2)

        # Create and save slices metadata
        train_slices_meta = create_slices_metadata(
            args.dataset_name, fold_idx, 'train', train_indices, args.seq_len
        )
        val_slices_meta = create_slices_metadata(
            args.dataset_name, fold_idx, 'val', val_indices, args.seq_len
        )

        train_slices_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_slices_meta_train.json"
        val_slices_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_slices_meta_val.json"

        with open(train_slices_file, 'w') as f:
            json.dump(train_slices_meta, f, indent=2)
        with open(val_slices_file, 'w') as f:
            json.dump(val_slices_meta, f, indent=2)

        print(f"  Fold {fold_idx}: {len(train_indices)} train, {len(val_indices)} val samples")

    print(f"\n✅ Dataset creation complete!")
    print(f"Files saved to: {output_dir}")
    print(f"Dataset name: {args.dataset_name}")
    print(f"Total samples: {n_samples}")
    print(f"Sequence length: {args.seq_len}")
    print(f"Features: {args.logs}")
    print(f"Number of folds: {args.n_folds}")
    print(f"Wells processed: {len(processed_data['WELL'].unique())}")


if __name__ == "__main__":
    main()
