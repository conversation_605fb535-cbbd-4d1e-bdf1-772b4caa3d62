#!/usr/bin/env python3
"""
Test script to verify that main.py can execute successfully with the dataset path fix.
"""

import sys
import subprocess
import os
from pathlib import Path

def test_main_help():
    """Test that main.py can show help without errors."""
    print("🔍 Testing main.py --help...")
    
    try:
        result = subprocess.run([
            sys.executable, "main.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ main.py --help executed successfully")
            return True
        else:
            print(f"❌ main.py --help failed with return code: {result.returncode}")
            print(f"   stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ main.py --help timed out")
        return False
    except Exception as e:
        print(f"❌ Error running main.py --help: {e}")
        return False

def test_main_config_validation():
    """Test that main.py validates configuration correctly."""
    print("\n🔍 Testing main.py configuration validation...")
    
    try:
        # Test with default configuration (should work now)
        result = subprocess.run([
            sys.executable, "main.py", 
            "--model", "locf",  # Use a simple model that doesn't require training
            "--epochs", "1",    # Minimal epochs for quick test
            "--n_folds", "1"    # Test with just one fold
        ], capture_output=True, text=True, timeout=60)
        
        if "Dataset 'real_las_dataset' found in 'bkp_data'" in result.stderr:
            print("✅ main.py found the dataset correctly")
            print("✅ Configuration validation is working")
            return True
        else:
            print(f"❌ main.py did not find dataset correctly")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ main.py configuration test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running main.py configuration test: {e}")
        return False

def test_main_with_test_dataset():
    """Test that main.py can run with test_dataset."""
    print("\n🔍 Testing main.py with test_dataset...")
    
    try:
        # Test with test_dataset
        result = subprocess.run([
            sys.executable, "main.py", 
            "--dataset_name", "test_dataset",
            "--model", "locf",  # Use a simple model
            "--epochs", "1",    # Minimal epochs
            "--n_folds", "1"    # Test with just one fold
        ], capture_output=True, text=True, timeout=60)
        
        if "Dataset 'test_dataset' found in 'bkp_data'" in result.stderr:
            print("✅ main.py found test_dataset correctly")
            return True
        else:
            print(f"❌ main.py did not find test_dataset correctly")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ main.py test_dataset test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running main.py test_dataset test: {e}")
        return False

def test_main_with_invalid_dataset():
    """Test that main.py fails gracefully with invalid dataset."""
    print("\n🔍 Testing main.py with invalid dataset...")
    
    try:
        # Test with invalid dataset
        result = subprocess.run([
            sys.executable, "main.py", 
            "--dataset_name", "invalid_dataset",
            "--model", "locf"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0 and "not found in" in result.stdout:
            print("✅ main.py correctly rejected invalid dataset")
            print("✅ Error handling is working properly")
            return True
        else:
            print(f"❌ main.py should have failed but didn't")
            print(f"   return code: {result.returncode}")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ main.py invalid dataset test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running main.py invalid dataset test: {e}")
        return False

def test_main_with_invalid_directory():
    """Test that main.py fails gracefully with invalid directory."""
    print("\n🔍 Testing main.py with invalid directory...")
    
    try:
        # Test with invalid directory
        result = subprocess.run([
            sys.executable, "main.py", 
            "--dataset_dir", "nonexistent_directory",
            "--model", "locf"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0 and "does not exist" in result.stdout:
            print("✅ main.py correctly rejected invalid directory")
            print("✅ Directory validation is working properly")
            return True
        else:
            print(f"❌ main.py should have failed but didn't")
            print(f"   return code: {result.returncode}")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ main.py invalid directory test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running main.py invalid directory test: {e}")
        return False

def check_environment():
    """Check that the environment is set up correctly."""
    print("🔍 Checking environment setup...")
    
    # Check that main.py exists
    if not Path("main.py").exists():
        print("❌ main.py not found")
        return False
    
    # Check that bkp_data exists
    if not Path("bkp_data").exists():
        print("❌ bkp_data directory not found")
        return False
    
    # Check that datasets exist
    train_files = list(Path("bkp_data").glob("*_sliced_train.npy"))
    if not train_files:
        print("❌ No dataset files found in bkp_data")
        return False
    
    print(f"✅ Environment check passed")
    print(f"   Found {len(train_files)} training files")
    return True

def main():
    """Run all tests."""
    print("🚀 Testing main.py execution with dataset path fix...\n")
    
    # First check environment
    if not check_environment():
        print("❌ Environment check failed. Cannot proceed with tests.")
        return 1
    
    tests = [
        ("Main Help", test_main_help),
        ("Configuration Validation", test_main_config_validation),
        ("Test Dataset", test_main_with_test_dataset),
        ("Invalid Dataset", test_main_with_invalid_dataset),
        ("Invalid Directory", test_main_with_invalid_directory),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"Running {test_name} Test")
        print(f"{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print(f"{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! main.py is working correctly with the dataset path fix.")
        print("✅ The dataset path error has been resolved.")
        print("✅ main.py can now find and validate datasets properly.")
        print("✅ Error handling provides helpful messages for invalid inputs.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print(f"{'='*60}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
