'''
This module handles all data loading and preprocessing operations.
'''

import os
import numpy as np
import pandas as pd
import lasio
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from typing import List, Dict, Tuple, Optional, Any
import warnings


def load_las_files(las_folder: str, selected_logs: List[str], std_names: Dict[str, str]) -> Optional[pd.DataFrame]:
    """
    Loads all .las files from a given folder into a single pandas DataFrame.

    Args:
        las_folder: Path to the folder containing .las files.
        selected_logs: A list of log mnemonics to be loaded from the las files.
        std_names: A dictionary to map original log names to standardized names.

    Returns:
        A pandas DataFrame containing the concatenated well log data, or None if the folder is empty.
    """
    data = None
    
    if not os.path.isdir(las_folder):
        print(f"Error: Directory not found at {las_folder}")
        return None

    las_files = [f for f in os.listdir(las_folder) if f.lower().endswith('.las')]
    if not las_files:
        print(f"No .las files found in {las_folder}")
        return None

    for las_filename in las_files:
        las_filepath = os.path.join(las_folder, las_filename)
        try:
            las_obj = lasio.read(las_filepath)
            df = las_obj.df().reset_index()
            df = df.rename(columns=std_names)

            # Standardize GR column name
            if 'GR' not in df.columns:
                if 'GRN' in df.columns:
                    df = df.rename(columns={'GRN': 'GR'})
                elif 'GRD' in df.columns:
                    df = df.rename(columns={'GRD': 'GR'})
            
            # Ensure all selected logs are present, fill with NaN if not
            for log in selected_logs:
                if log not in df.columns:
                    df[log] = np.nan

            df_filtered = df[selected_logs]
            df_filtered = df_filtered.assign(WELL=os.path.splitext(las_filename)[0])

            if data is None:
                data = df_filtered
            else:
                data = pd.concat([data, df_filtered], ignore_index=True)
        except Exception as e:
            print(f"Could not process file {las_filename}: {e}")

    return data

def _winsorize(data: pd.DataFrame, logs: List[str]) -> pd.DataFrame:
    """
    Applies winsorization to the specified logs in the DataFrame.
    Values outside the 1st and 99th percentiles are replaced with NaN.
    """
    for log in logs:
        if log in data.columns:
            log_data = data[log].values
            min_val, max_val = np.nanquantile(log_data, q=[0.01, 0.99])
            outlier_idx = (log_data < min_val) | (log_data > max_val)
            log_data[outlier_idx] = np.nan
            data[log] = log_data
    return data

def _normalize(data: pd.DataFrame, logs: List[str]) -> pd.DataFrame:
    """
    Applies standard scaling (mean 0, std 1) to the specified logs.
    """
    scaler = StandardScaler()
    logs_to_normalize = [log for log in logs if log in data.columns]
    if logs_to_normalize:
        data[logs_to_normalize] = scaler.fit_transform(data[logs_to_normalize])
    return data

def preprocess_data(data: pd.DataFrame, logs: List[str]) -> pd.DataFrame:
    """
    Applies a preprocessing pipeline (winsorization and normalization) to the data.

    Args:
        data: The input DataFrame.
        logs: The list of logs to preprocess.

    Returns:
        The preprocessed DataFrame.
    """
    data = _winsorize(data, logs)
    data = _normalize(data, logs)
    return data

def _get_valid_intervals(log_array: np.ndarray) -> List[Tuple[int, int]]:
    """
    Identifies contiguous intervals of valid (non-NaN) data in a numpy array.
    """
    log_is_notnan = ~np.isnan(log_array)
    all_valid = np.all(log_is_notnan, axis=1)
    breaks = [0] + (np.where(all_valid[:-1] != all_valid[1:])[0] + 1).tolist() + [all_valid.size]

    valid_intervals = []
    for i in range(len(breaks) - 1):
        if all_valid[breaks[i]]:
            valid_intervals.append((breaks[i], breaks[i + 1]))
    return valid_intervals

def slice_data(data: pd.DataFrame, selected_logs: List[str], slice_size: int = 256, slice_stride: int = 256) -> Tuple[np.ndarray, list, list]:
    """
    Slices well log data into windows of a specified size and stride.

    Args:
        data: DataFrame containing the well log data with a 'WELL' column.
        selected_logs: The logs to be included in the slices.
        slice_size: The length of each slice (window).
        slice_stride: The step size between the start of each slice.

    Returns:
        A tuple containing:
        - A numpy array of the sliced data.
        - A list of metadata for each slice.
        - A list of metadata for each well.
    """
    slices_list = []
    slices_metadata = []
    fold_metadata = []

    well_ids = data['WELL'].dropna().unique()

    for well_id in well_ids:
        sz = len(slices_list)
        well_data = data.loc[data['WELL'] == well_id, selected_logs].values
        
        valid_intervals = _get_valid_intervals(well_data)

        for start_interval, end_interval in valid_intervals:
            start = start_interval
            while (start + slice_size) <= end_interval:
                end = start + slice_size
                slice_data_array = well_data[start:end, :]
                slices_list.append(slice_data_array)
                slices_metadata.append((well_id, start, end))
                start += slice_stride
        
        fold_metadata.append((well_id, len(slices_list) - sz))

    if not slices_list:
        return np.array([]), [], []

    return np.stack(slices_list), slices_metadata, fold_metadata

# ============================================================================
# PHASE 2: ADVANCED DATA PREPROCESSING PIPELINE
# ============================================================================

def create_scaling_pipeline(df: pd.DataFrame, feature_cols: List[str], target_col: str,
                          strategy: str = 'standard') -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Create and fit scalers for features and target as per guidelines.

    Args:
        df: Input DataFrame
        feature_cols: List of feature column names
        target_col: Target column name
        strategy: Scaling strategy ('standard', 'minmax', 'robust')

    Returns:
        Tuple of (scaled_dataframe, scalers_dict)
    """
    scalers = {}
    scaled_df = df.copy()

    # Choose scaler based on strategy
    scaler_map = {
        'standard': StandardScaler,
        'minmax': MinMaxScaler,
        'robust': RobustScaler
    }

    if strategy not in scaler_map:
        raise ValueError(f"Unknown scaling strategy: {strategy}. Choose from {list(scaler_map.keys())}")

    scaler_class = scaler_map[strategy]

    # Scale each column individually
    for col in feature_cols + [target_col]:
        if col in scaled_df.columns:
            scaler = scaler_class()
            # Handle missing values by fitting only on non-null values
            non_null_mask = scaled_df[col].notna()
            if non_null_mask.sum() > 0:
                # Fit scaler on non-null values
                scaler.fit(scaled_df.loc[non_null_mask, [col]])
                # Transform all values (including nulls which will remain null)
                scaled_values = scaler.transform(scaled_df[[col]])
                scaled_df[col] = scaled_values.flatten()
                scalers[col] = scaler
            else:
                print(f"⚠️ Warning: Column {col} has no non-null values for scaling")

    print(f"✅ Scaling pipeline created with {strategy} strategy for {len(scalers)} columns")
    return scaled_df, scalers

def inverse_transform_predictions(predictions: np.ndarray, scalers: Dict[str, Any],
                                target_col: str) -> np.ndarray:
    """
    Inverse transform predictions back to original scale as per guidelines.

    Args:
        predictions: Scaled predictions array
        scalers: Dictionary of fitted scalers
        target_col: Target column name

    Returns:
        Predictions in original scale
    """
    if target_col not in scalers:
        print(f"⚠️ Warning: No scaler found for {target_col}, returning predictions as-is")
        return predictions

    scaler = scalers[target_col]

    # Ensure predictions are in the right shape for inverse transform
    if predictions.ndim == 1:
        predictions_reshaped = predictions.reshape(-1, 1)
    else:
        predictions_reshaped = predictions

    # Inverse transform
    original_scale_predictions = scaler.inverse_transform(predictions_reshaped)

    # Return in original shape
    if predictions.ndim == 1:
        return original_scale_predictions.flatten()
    else:
        return original_scale_predictions

def create_sequences(df: pd.DataFrame, well_col: str, feature_cols: List[str],
                    target_col: str, sequence_length: int = 64, stride: int = 1) -> Tuple[np.ndarray, np.ndarray, List[Dict]]:
    """
    Create overlapping sequences for deep learning models as per guidelines.

    Args:
        df: Input DataFrame
        well_col: Well identifier column name
        feature_cols: List of feature column names
        target_col: Target column name
        sequence_length: Length of each sequence
        stride: Step size between sequence starts

    Returns:
        Tuple of (sequences_array, targets_array, metadata_list)
    """
    sequences = []
    targets = []
    metadata = []  # Store well, start_idx, end_idx for reconstruction

    print(f"🔄 Creating sequences with length={sequence_length}, stride={stride}")

    for well in df[well_col].unique():
        well_data = df[df[well_col] == well].sort_values('MD').reset_index(drop=True)

        if len(well_data) < sequence_length:
            print(f"⚠️ Warning: Well {well} has only {len(well_data)} points, less than sequence_length={sequence_length}")
            continue

        # Create sliding windows
        for i in range(0, len(well_data) - sequence_length + 1, stride):
            # Extract sequence data
            seq_features = well_data[feature_cols].iloc[i:i+sequence_length].values
            seq_target = well_data[target_col].iloc[i:i+sequence_length].values
            seq_depths = well_data['MD'].iloc[i:i+sequence_length].values

            # Check if sequence has sufficient non-null data
            feature_valid_ratio = (~np.isnan(seq_features)).mean()
            target_valid_ratio = (~np.isnan(seq_target)).mean()

            if feature_valid_ratio > 0.5 and target_valid_ratio > 0.3:  # Configurable thresholds
                sequences.append(seq_features)
                targets.append(seq_target)
                metadata.append({
                    'well': well,
                    'start_idx': i,
                    'end_idx': i + sequence_length,
                    'depths': seq_depths,
                    'feature_valid_ratio': feature_valid_ratio,
                    'target_valid_ratio': target_valid_ratio
                })

    if not sequences:
        print("❌ No valid sequences created")
        return np.array([]), np.array([]), []

    sequences_array = np.array(sequences)
    targets_array = np.array(targets)

    print(f"✅ Created {len(sequences)} sequences from {df[well_col].nunique()} wells")
    print(f"   Sequence shape: {sequences_array.shape}")
    print(f"   Target shape: {targets_array.shape}")

    return sequences_array, targets_array, metadata

def handle_missing_values(df: pd.DataFrame, well_col: str = 'WELL',
                         strategy: str = 'interpolate') -> pd.DataFrame:
    """
    Handle missing values with various strategies as per guidelines.

    Args:
        df: Input DataFrame
        well_col: Well identifier column name
        strategy: Missing value strategy ('interpolate', 'forward_fill', 'mean_fill', 'median_fill')

    Returns:
        DataFrame with missing values handled
    """
    strategies = {
        'interpolate': lambda x: x.interpolate(method='linear', limit_direction='both'),
        'forward_fill': lambda x: x.fillna(method='ffill'),
        'backward_fill': lambda x: x.fillna(method='bfill'),
        'mean_fill': lambda x: x.fillna(x.mean()),
        'median_fill': lambda x: x.fillna(x.median())
    }

    if strategy not in strategies:
        raise ValueError(f"Unknown strategy: {strategy}. Choose from {list(strategies.keys())}")

    print(f"🔄 Handling missing values with strategy: {strategy}")

    # Apply strategy per well to maintain well-specific characteristics
    result_df = df.groupby(well_col).apply(strategies[strategy]).reset_index(drop=True)

    # Report missing value statistics
    original_missing = df.isnull().sum().sum()
    final_missing = result_df.isnull().sum().sum()

    print(f"✅ Missing values: {original_missing} → {final_missing} "
          f"({((original_missing - final_missing) / max(original_missing, 1) * 100):.1f}% reduction)")

    return result_df

def validate_data_quality(df: pd.DataFrame, feature_cols: List[str],
                         target_col: str, well_col: str = 'WELL') -> List[str]:
    """
    Validate data quality and return warnings as per guidelines.

    Args:
        df: Input DataFrame
        feature_cols: List of feature column names
        target_col: Target column name
        well_col: Well identifier column name

    Returns:
        List of warning messages
    """
    warnings_list = []

    print("🔍 Validating data quality...")

    # Check for excessive missing values
    all_cols = feature_cols + [target_col]
    missing_pct = df[all_cols].isnull().mean()
    high_missing = missing_pct[missing_pct > 0.5]

    if not high_missing.empty:
        warning_msg = f"High missing values (>50%): {high_missing.to_dict()}"
        warnings_list.append(warning_msg)
        print(f"⚠️ {warning_msg}")

    # Check for constant columns
    constant_cols = []
    for col in all_cols:
        if col in df.columns:
            non_null_values = df[col].dropna()
            if len(non_null_values) > 0 and non_null_values.nunique() <= 1:
                constant_cols.append(col)

    if constant_cols:
        warning_msg = f"Constant columns detected: {constant_cols}"
        warnings_list.append(warning_msg)
        print(f"⚠️ {warning_msg}")

    # Check for wells with insufficient data
    well_data_counts = df.groupby(well_col).size()
    short_wells = well_data_counts[well_data_counts < 50]  # Configurable threshold

    if not short_wells.empty:
        warning_msg = f"Wells with <50 data points: {short_wells.to_dict()}"
        warnings_list.append(warning_msg)
        print(f"⚠️ {warning_msg}")

    # Check for outliers using IQR method
    for col in feature_cols + [target_col]:
        if col in df.columns:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
            outlier_pct = len(outliers) / len(df[col].dropna()) * 100

            if outlier_pct > 5:  # More than 5% outliers
                warning_msg = f"High outlier percentage in {col}: {outlier_pct:.1f}%"
                warnings_list.append(warning_msg)
                print(f"⚠️ {warning_msg}")

    # Check data distribution balance across wells
    well_target_coverage = df.groupby(well_col)[target_col].apply(lambda x: x.notna().mean())
    unbalanced_wells = well_target_coverage[well_target_coverage < 0.3]

    if not unbalanced_wells.empty:
        warning_msg = f"Wells with low target coverage (<30%): {unbalanced_wells.to_dict()}"
        warnings_list.append(warning_msg)
        print(f"⚠️ {warning_msg}")

    if not warnings_list:
        print("✅ Data quality validation passed - no major issues detected")
    else:
        print(f"⚠️ Data quality validation completed with {len(warnings_list)} warnings")

    return warnings_list

def handle_sequence_edge_cases(df: pd.DataFrame, sequence_length: int,
                              well_col: str = 'WELL') -> Tuple[List[Tuple], str]:
    """
    Handle wells shorter than sequence length as per guidelines.

    Args:
        df: Input DataFrame
        sequence_length: Required sequence length
        well_col: Well identifier column name

    Returns:
        Tuple of (short_wells_list, recommendation)
    """
    short_wells = []

    for well in df[well_col].unique():
        well_len = len(df[df[well_col] == well])
        if well_len < sequence_length:
            short_wells.append((well, well_len))

    if short_wells:
        print(f"⚠️ Warning: {len(short_wells)} wells shorter than sequence length ({sequence_length})")
        for well, length in short_wells:
            print(f"   • {well}: {length} points")

        # Provide recommendations
        if len(short_wells) / df[well_col].nunique() > 0.3:
            recommendation = f"Consider reducing sequence_length to {min([length for _, length in short_wells])}"
        else:
            recommendation = "Consider excluding short wells or using padding strategy"

        print(f"💡 Recommendation: {recommendation}")
        return short_wells, recommendation

    print("✅ All wells have sufficient length for sequence creation")
    return [], "No action needed"
