#!/usr/bin/env python3
"""
Test script to verify that the dataset path fix works correctly.
"""

import sys
import os
from pathlib import Path

def test_cfg_with_default_dataset():
    """Test the configuration with default dataset directory."""
    print("🔍 Testing configuration with default dataset directory...")
    
    try:
        from cfg import Configs
        
        # Test with default arguments (should use bkp_data and find datasets)
        sys.argv = ['test_script']  # Reset argv to avoid conflicts
        cfg = Configs()
        
        # Parse with default arguments
        args = cfg.parse_args()
        
        print(f"✅ Configuration parsed successfully!")
        print(f"   Dataset directory: {args.dataset_dir}")
        print(f"   Dataset name: {args.dataset_name}")
        print(f"   Number of folds: {args.n_folds}")
        print(f"   Number of features: {args.n_features}")
        
        return True
        
    except SystemExit as e:
        print(f"❌ Configuration failed with exit code: {e.code}")
        return False
    except Exception as e:
        print(f"❌ Configuration failed with error: {e}")
        return False

def test_cfg_with_real_dataset():
    """Test the configuration with real_las_dataset."""
    print("\n🔍 Testing configuration with real_las_dataset...")
    
    try:
        from cfg import Configs
        
        # Test with real_las_dataset
        sys.argv = ['test_script', '--dataset_name', 'real_las_dataset']
        cfg = Configs()
        
        # Parse with real dataset
        args = cfg.parse_args()
        
        print(f"✅ Configuration with real_las_dataset parsed successfully!")
        print(f"   Dataset directory: {args.dataset_dir}")
        print(f"   Dataset name: {args.dataset_name}")
        
        return True
        
    except SystemExit as e:
        print(f"❌ Configuration failed with exit code: {e.code}")
        return False
    except Exception as e:
        print(f"❌ Configuration failed with error: {e}")
        return False

def test_cfg_with_test_dataset():
    """Test the configuration with test_dataset."""
    print("\n🔍 Testing configuration with test_dataset...")
    
    try:
        from cfg import Configs
        
        # Test with test_dataset
        sys.argv = ['test_script', '--dataset_name', 'test_dataset']
        cfg = Configs()
        
        # Parse with test dataset
        args = cfg.parse_args()
        
        print(f"✅ Configuration with test_dataset parsed successfully!")
        print(f"   Dataset directory: {args.dataset_dir}")
        print(f"   Dataset name: {args.dataset_name}")
        
        return True
        
    except SystemExit as e:
        print(f"❌ Configuration failed with exit code: {e.code}")
        return False
    except Exception as e:
        print(f"❌ Configuration failed with error: {e}")
        return False

def test_cfg_with_nonexistent_dataset():
    """Test the configuration with a non-existent dataset (should fail gracefully)."""
    print("\n🔍 Testing configuration with non-existent dataset...")
    
    try:
        from cfg import Configs
        
        # Test with non-existent dataset
        sys.argv = ['test_script', '--dataset_name', 'nonexistent_dataset']
        cfg = Configs()
        
        # This should fail
        args = cfg.parse_args()
        
        print(f"❌ Configuration should have failed but didn't!")
        return False
        
    except SystemExit as e:
        print(f"✅ Configuration correctly failed with exit code: {e.code}")
        print(f"   This is expected behavior for non-existent datasets")
        return True
    except Exception as e:
        print(f"❌ Configuration failed with unexpected error: {e}")
        return False

def test_loading_data_function():
    """Test the loading_data function with available datasets."""
    print("\n🔍 Testing loading_data function...")
    
    try:
        from main import loading_data
        
        # Test with real_las_dataset, fold 0
        dataset_name = "real_las_dataset"
        data_dir = "bkp_data"
        fold = 0
        
        print(f"   Attempting to load {dataset_name} fold {fold} from {data_dir}...")
        data_train, data_val = loading_data(dataset_name, data_dir, fold)
        
        print(f"✅ Successfully loaded data!")
        print(f"   Training data shape: {data_train.shape}")
        print(f"   Validation data shape: {data_val.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return False

def check_available_datasets():
    """Check what datasets are available in the bkp_data directory."""
    print("\n📁 Checking available datasets...")
    
    try:
        data_dir = Path("bkp_data")
        if not data_dir.exists():
            print(f"❌ Directory {data_dir} does not exist")
            return False
        
        # Find all training files
        train_files = list(data_dir.glob("*_sliced_train.npy"))
        if not train_files:
            print(f"❌ No training files found in {data_dir}")
            return False
        
        # Extract dataset names
        datasets = set([f.stem.split('_fold_')[0] for f in train_files])
        print(f"✅ Found {len(datasets)} datasets: {', '.join(sorted(datasets))}")
        
        # Check folds for each dataset
        for dataset in sorted(datasets):
            folds = []
            for f in train_files:
                if f.stem.startswith(dataset):
                    fold_part = f.stem.split('_fold_')[1].split('_')[0]
                    folds.append(int(fold_part))
            
            print(f"   {dataset}: {len(folds)} folds ({min(folds)}-{max(folds)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking datasets: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing dataset path fix...\n")
    
    tests = [
        ("Check Available Datasets", check_available_datasets),
        ("Default Configuration", test_cfg_with_default_dataset),
        ("Real Dataset Configuration", test_cfg_with_real_dataset),
        ("Test Dataset Configuration", test_cfg_with_test_dataset),
        ("Non-existent Dataset Configuration", test_cfg_with_nonexistent_dataset),
        ("Loading Data Function", test_loading_data_function),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"Running {test_name}")
        print(f"{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print(f"{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! The dataset path fix is working correctly.")
        print("✅ main.py should now be able to find and load datasets.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print(f"{'='*60}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())
