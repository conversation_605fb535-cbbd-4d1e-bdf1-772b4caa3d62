#!/usr/bin/env python3
"""
Test script to verify that the PyPOTS metrics import fix works correctly.
"""

import numpy as np

def test_metrics_import():
    """Test the corrected PyPOTS metrics import."""
    print("Testing PyPOTS metrics import fix...")
    
    try:
        # Test the corrected import
        from pypots.nn.functional import calc_mae, calc_mse, calc_rmse
        print("✓ Successfully imported calc_mae, calc_mse, calc_rmse from pypots.nn.functional")
        
        # Create test data
        predictions = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
        targets = np.array([[1.1, 2.1, 2.9], [3.9, 5.1, 6.1]])
        mask = np.array([[True, True, True], [True, True, True]])
        
        # Test the functions
        mae = calc_mae(predictions, targets, mask)
        mse = calc_mse(predictions, targets, mask)
        rmse = calc_rmse(predictions, targets, mask)
        
        print(f"✓ calc_mae result: {mae:.4f}")
        print(f"✓ calc_mse result: {mse:.4f}")
        print(f"✓ calc_rmse result: {rmse:.4f}")
        
        # Verify the results make sense
        if mae > 0 and mse > 0 and rmse > 0:
            print("✓ All metric functions returned positive values as expected")
        else:
            print("✗ Unexpected metric values")
            return False
            
        # Test with missing values (partial mask)
        partial_mask = np.array([[True, False, True], [False, True, True]])
        mae_partial = calc_mae(predictions, targets, partial_mask)
        print(f"✓ calc_mae with partial mask: {mae_partial:.4f}")
        
        print("\n🎉 All tests passed! The PyPOTS metrics import has been fixed successfully.")
        return True
        
    except ImportError as e:
        print(f"❌ ImportError still exists: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error occurred: {e}")
        return False

def test_main_import():
    """Test that main.py can now be imported without errors."""
    print("\nTesting main.py import...")
    
    try:
        # Test importing specific functions from main.py
        from main import masked_fill, select_optimizer, get_dataset_dict
        print("✓ Successfully imported functions from main.py")
        
        # Test that the metrics are accessible (they should be imported in main.py)
        import main
        # The functions should be available in the main module's namespace after import
        print("✓ main.py imported successfully without ImportError")
        
        return True
        
    except ImportError as e:
        print(f"❌ ImportError in main.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error in main.py: {e}")
        return False

if __name__ == "__main__":
    success1 = test_metrics_import()
    success2 = test_main_import()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The ImportError has been completely fixed.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
