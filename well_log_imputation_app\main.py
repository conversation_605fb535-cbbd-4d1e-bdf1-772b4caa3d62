'''
Main orchestrator script for the well log imputation application.
'''

import argparse
import os
import numpy as np
import sys

# Import functions from our refactored modules
from . import config_handler
from . import data_handler
from . import ml_core
from . import reporting

def main():
    """
    Main function to run the imputation workflow.
    """
    # Define the path for the configuration file, ensuring it's in the app's directory
    # Get the directory where the main.py script is located
    app_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(app_dir, 'config.json')

    # 1. Setup argument parser to get config file path
    parser = argparse.ArgumentParser(description='Well Log Imputation Application')
    parser.add_argument('--config', type=str, default=config_path, 
                        help='Path to the configuration file.')
    args = parser.parse_args()

    # 2. Load configuration
    print("Loading configuration...")
    # Check if the config file exists, if not, create a default one
    if not os.path.exists(args.config):
        print(f"Configuration file not found at {args.config}. Creating a default one.")
        config_handler.save_config(config_handler.DEFAULT_CONFIG, args.config)
    
    config = config_handler.load_config(args.config)

    # Extract settings from config
    data_settings = config.get('data_settings', {})
    processing_settings = config.get('processing_settings', {})
    model_settings = config.get('model_settings', {})
    output_settings = config.get('output_settings', {})

    # Create output directory if it doesn't exist
    os.makedirs(output_settings['save_path'], exist_ok=True)

    # 3. Load data
    print("Loading data...")
    raw_data = data_handler.load_las_files(
        las_folder=data_settings['las_folder'],
        selected_logs=data_settings['selected_logs'],
        std_names=data_settings['std_names']
    )

    if raw_data is None:
        print("Data loading failed. Exiting.")
        return

    # 4. Preprocess data
    print("Preprocessing data...")
    preprocessed_data = data_handler.preprocess_data(raw_data.copy(), data_settings['selected_logs'])

    # 5. Slice data
    print("Slicing data...")
    sliced_data, _, _ = data_handler.slice_data(
        preprocessed_data,
        selected_logs=data_settings['selected_logs'],
        slice_size=processing_settings['slice_size'],
        slice_stride=processing_settings['slice_stride']
    )

    if sliced_data.size == 0:
        print("No valid data slices found. Exiting.")
        return

    # For simplicity, we'll use all sliced data for training.
    # In a real scenario, you would split this into train/validation sets.
    train_data = sliced_data

    # Create a mask of missing values from the original sliced data
    missing_mask = np.isnan(train_data)

    # 5. Get model config and initialize model
    model_settings = config['model_settings']
    model_config = config_handler.get_model_config(config, model_settings['name'])

    # Add n_features and n_steps to the model config, as they are required by many models
    model_config['n_features'] = len(data_settings['selected_logs'])
    model_config['n_steps'] = processing_settings['slice_size']
    model = ml_core.get_model(model_settings['name'], model_config)

    if model is None:
        print("Model initialization failed. Exiting.")
        return

    # 7. Train model
    print("Training model...")
    # Most PyPOTS models are designed to be trained on data with missing values (NaNs).
    # We will pass the data with NaNs directly.
    ml_core.train_model(model, train_data)

    # 8. Make predictions
    print("Making predictions...")
    # The model also expects data with NaNs for prediction.
    imputed_data = ml_core.predict_with_model(model, train_data)

    # The model outputs the complete dataset, so we use the mask to fill in the original
    # We also need to fill the original non-missing values back in.
    train_data_filled = np.nan_to_num(train_data)
    final_imputed_data = np.where(missing_mask, imputed_data, train_data_filled)

    # 9. Generate report
    print("Generating report...")
    # For demonstration, we'll just plot the first well slice
    # A real implementation would loop through wells or slices
    plot_path = os.path.join(output_settings['save_path'], 'well_slice_0_plot.png')
    reporting.plot_well_logs(
        original_data=train_data[0],
        imputed_data=final_imputed_data[0],
        missing_mask=missing_mask[0],
        log_names=data_settings['selected_logs'],
        well_id='slice_0',
        output_path=plot_path
    )

    report_path = os.path.join(output_settings['save_path'], output_settings['report_name'])
    reporting.generate_report(
        config=config,
        well_plots=[plot_path],
        output_path=report_path
    )

    # 10. Save results
    print("Saving imputed data...")
    imputed_data_path = os.path.join(output_settings['save_path'], 'imputed_data.npy')
    np.save(imputed_data_path, final_imputed_data)
    print(f"Imputed data saved to {imputed_data_path}")

    # Save the configuration used for this run
    config_handler.save_config(config, os.path.join(output_settings['save_path'], 'session_config.json'))

    print("\nWorkflow complete!")


