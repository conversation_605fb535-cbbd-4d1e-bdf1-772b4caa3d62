#!/usr/bin/env python3
"""
ML Log Prediction with GUI file selection dialog for LAS files.
"""

from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
from config_handler import get_input_files, select_output_directory, configure_log_selection, configure_well_separation, get_prediction_mode, configure_hyperparameters
from ml_core import impute_logs, MODEL_REGISTRY
from reporting import generate_qc_report, create_summary_plots, generate_final_report

def main():
    """Run the ML log prediction workflow with GUI file selection."""
    print("=" * 60)
    print(" ML LOG PREDICTION")
    print("=" * 60)
    
    # Step 1: Get input files using file dialog
    print("\n📁 Step 1: Select LAS files")
    inp = get_input_files()
    
    if not inp:
        print("❌ File selection cancelled. Exiting.")
        return
    
    # Step 2: Load LAS files
    print("\n📊 Step 2: Loading LAS files...")
    df, las_objs, wells, logs = load_las_files_from_directory(inp)
    
    if df.empty:
        print("❌ No data loaded. Exiting.")
        return
    
    print(f"✅ Successfully loaded:")
    print(f"   • {len(df)} data points")
    print(f"   • {len(wells)} wells: {', '.join(wells)}")
    print(f"   • {len(logs)} log curves: {', '.join(logs)}")
    
    # Step 3: Configure log selection
    print("\n🎯 Step 3: Configure feature and target logs")
    feats, tgt = configure_log_selection(logs)
    print(f"✅ Feature logs: {', '.join(feats)}")
    print(f"✅ Target log: {tgt}")
    
    # Step 4: Configure well separation
    print("\n🏗️ Step 4: Configure training/prediction strategy")
    cfg = configure_well_separation(wells)
    print(f"✅ Mode: {cfg['mode']}")
    
    # Step 5: Configure prediction mode
    print("\n⚙️ Step 5: Configure prediction mode")
    mode = get_prediction_mode()
    print(f"✅ Prediction mode: {mode}")
    
    # Step 6: Configure hyperparameters
    print("\n🔧 Step 6: Configure model hyperparameters")
    hparams = configure_hyperparameters()
    print("✅ Using default hyperparameters for all models")
    
    # Step 7: Data cleaning and QC
    print("\n🧹 Step 7: Data cleaning and quality control")
    clean_df = clean_log_data(df)
    generate_qc_report(clean_df, feats+[tgt], cfg)
    
    # Step 8: Machine learning prediction
    print("\n🤖 Step 8: Running machine learning models...")
    models = {MODEL_REGISTRY[k]['name']: MODEL_REGISTRY[k]['model_class'](**hparams[k]) for k in MODEL_REGISTRY}
    res_df, mres = impute_logs(clean_df, feats, tgt, models, cfg, mode)
    
    if not mres:
        print("❌ Model training failed. Exiting.")
        return
    
    print("✅ Machine learning prediction completed")
    
    # Step 9: Configure output options
    print("\n💾 Step 9: Configure output options")
    print("Do you want to save the results?")
    print("1. Yes, save results to files")
    print("2. No, just plot and test (no file output)")
    
    while True:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            # Get output directory
            print("\n📁 Select output directory...")
            out = select_output_directory()
            if not out:
                print("❌ Output directory selection cancelled. Exiting.")
                return
            
            # Generate and save results
            print("\n📈 Step 10: Generating and saving results...")
            create_summary_plots(res_df, mres, cfg)
            write_results_to_las(res_df, tgt, las_objs, out)
            generate_final_report(mres, hparams)
            
            print("\n🎉 Workflow completed successfully!")
            print(f"📁 Results saved to: {out}")
            break
        elif choice == "2":
            # Just show plots and testing without saving
            print("\n📈 Step 10: Generating plots and testing (no file output)...")
            create_summary_plots(res_df, mres, cfg)
            
            print("\n🎉 Analysis completed successfully!")
            print("📊 Plots generated for review (no files saved)")
            break
        else:
            print("Invalid choice. Please enter 1 or 2.")

if __name__ == "__main__":
    main()
