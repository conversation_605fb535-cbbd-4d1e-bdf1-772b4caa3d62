'''
This module contains the core machine learning logic, including model
management, training, and prediction orchestration.
'''

import numpy as np
import pandas as pd
import torch
from typing import Dict, Any, Optional, Union, List, Tuple
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler

# Import model classes from the models package
try:
    from .models.autoencoder import Autoencoder
except ImportError:
    print("⚠️ Warning: Autoencoder model not available")
    Autoencoder = None

try:
    from .models.unet import UNet
except ImportError:
    print("⚠️ Warning: UNet model not available")
    UNet = None

try:
    from .models.shallow import RandomForest, XGBoost, SVM
except ImportError:
    print("⚠️ Warning: Some shallow models not available")
    RandomForest = XGBoost = SVM = None

try:
    from .models.locf import LOCF
except ImportError:
    print("⚠️ Warning: LOCF model not available")
    LOCF = None

try:
    from .models.pypots_models import SAITSImputer, TransformerImputer, BRITSImputer, MRNNImputer
except ImportError:
    print("⚠️ Warning: PyPOTS models not available")
    SAITSImputer = TransformerImputer = BRITSImputer = MRNNImputer = None

def detect_device():
    """Automatically detect the best available device (GPU/CPU)."""
    if torch.cuda.is_available():
        device = 'cuda'
        print(f"✅ GPU detected: {torch.cuda.get_device_name(0)}")
    else:
        device = 'cpu'
        print("💻 Using CPU")
    return device

# Enhanced MODEL_REGISTRY with comprehensive metadata as per guidelines
MODEL_REGISTRY = {}

# Only add models that are available
if Autoencoder is not None:
    MODEL_REGISTRY['autoencoder'] = {
        'name': 'Autoencoder',
        'model_class': Autoencoder,
        'type': 'deep',
        'hyperparameters': {
            'encoding_dim': {'type': int, 'default': 32, 'min': 16, 'max': 128, 'prompt': 'Encoding dimension'},
            'epochs': {'type': int, 'default': 50, 'min': 10, 'max': 200, 'prompt': 'Number of epochs'},
            'batch_size': {'type': int, 'default': 32, 'min': 16, 'max': 128, 'prompt': 'Batch size'},
            'learning_rate': {'type': float, 'default': 0.001, 'min': 0.0001, 'max': 0.01, 'prompt': 'Learning rate'},
            'sequence_length': {'type': int, 'default': 64, 'min': 32, 'max': 256, 'prompt': 'Sequence length'},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': 'Early stopping patience'}
        },
        'fixed_params': {'device': 'auto'}
    }

if UNet is not None:
    MODEL_REGISTRY['unet'] = {
        'name': 'U-Net',
        'model_class': UNet,
        'type': 'deep',
        'hyperparameters': {
            'epochs': {'type': int, 'default': 50, 'min': 10, 'max': 200, 'prompt': 'Number of epochs'},
            'batch_size': {'type': int, 'default': 32, 'min': 16, 'max': 128, 'prompt': 'Batch size'},
            'learning_rate': {'type': float, 'default': 0.001, 'min': 0.0001, 'max': 0.01, 'prompt': 'Learning rate'},
            'sequence_length': {'type': int, 'default': 64, 'min': 32, 'max': 256, 'prompt': 'Sequence length'},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': 'Early stopping patience'},
            'channels': {'type': list, 'default': [32, 64, 128, 256], 'prompt': 'Channel dimensions'}
        },
        'fixed_params': {'device': 'auto', 'spatial_dims': 1}
    }

if SAITSImputer is not None:
    MODEL_REGISTRY['saits'] = {
        'name': 'SAITS',
        'model_class': SAITSImputer,
        'type': 'deep',
        'hyperparameters': {
            'epochs': {'type': int, 'default': 100, 'min': 20, 'max': 300, 'prompt': 'Number of epochs'},
            'batch_size': {'type': int, 'default': 32, 'min': 16, 'max': 128, 'prompt': 'Batch size'},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': 'Early stopping patience'},
            'n_layers': {'type': int, 'default': 2, 'min': 1, 'max': 6, 'prompt': 'Number of layers'},
            'd_model': {'type': int, 'default': 256, 'min': 64, 'max': 512, 'prompt': 'Model dimension'},
            'd_inner': {'type': int, 'default': 128, 'min': 32, 'max': 256, 'prompt': 'Inner dimension'},
            'n_head': {'type': int, 'default': 4, 'min': 2, 'max': 16, 'prompt': 'Number of attention heads'},
            'd_k': {'type': int, 'default': 64, 'min': 16, 'max': 128, 'prompt': 'Key dimension'},
            'd_v': {'type': int, 'default': 64, 'min': 16, 'max': 128, 'prompt': 'Value dimension'},
            'dropout': {'type': float, 'default': 0.1, 'min': 0.0, 'max': 0.5, 'prompt': 'Dropout rate'}
        },
        'fixed_params': {'device': 'auto'}
    }

if TransformerImputer is not None:
    MODEL_REGISTRY['transformer'] = {
        'name': 'Transformer',
        'model_class': TransformerImputer,
        'type': 'deep',
        'hyperparameters': {
            'epochs': {'type': int, 'default': 100, 'min': 20, 'max': 300, 'prompt': 'Number of epochs'},
            'batch_size': {'type': int, 'default': 32, 'min': 16, 'max': 128, 'prompt': 'Batch size'},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': 'Early stopping patience'},
            'n_layers': {'type': int, 'default': 2, 'min': 1, 'max': 6, 'prompt': 'Number of layers'},
            'd_model': {'type': int, 'default': 256, 'min': 64, 'max': 512, 'prompt': 'Model dimension'},
            'd_inner': {'type': int, 'default': 128, 'min': 32, 'max': 256, 'prompt': 'Inner dimension'},
            'n_head': {'type': int, 'default': 4, 'min': 2, 'max': 16, 'prompt': 'Number of attention heads'},
            'd_k': {'type': int, 'default': 64, 'min': 16, 'max': 128, 'prompt': 'Key dimension'},
            'd_v': {'type': int, 'default': 64, 'min': 16, 'max': 128, 'prompt': 'Value dimension'},
            'dropout': {'type': float, 'default': 0.1, 'min': 0.0, 'max': 0.5, 'prompt': 'Dropout rate'}
        },
        'fixed_params': {'device': 'auto'}
    }

if BRITSImputer is not None:
    MODEL_REGISTRY['brits'] = {
        'name': 'BRITS',
        'model_class': BRITSImputer,
        'type': 'deep',
        'hyperparameters': {
            'epochs': {'type': int, 'default': 100, 'min': 20, 'max': 300, 'prompt': 'Number of epochs'},
            'batch_size': {'type': int, 'default': 32, 'min': 16, 'max': 128, 'prompt': 'Batch size'},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': 'Early stopping patience'},
            'rnn_hidden_size': {'type': int, 'default': 64, 'min': 32, 'max': 256, 'prompt': 'RNN hidden size'}
        },
        'fixed_params': {'device': 'auto'}
    }

if MRNNImputer is not None:
    MODEL_REGISTRY['mrnn'] = {
        'name': 'M-RNN',
        'model_class': MRNNImputer,
        'type': 'deep',
        'hyperparameters': {
            'epochs': {'type': int, 'default': 100, 'min': 20, 'max': 300, 'prompt': 'Number of epochs'},
            'batch_size': {'type': int, 'default': 32, 'min': 16, 'max': 128, 'prompt': 'Batch size'},
            'patience': {'type': int, 'default': 10, 'min': 5, 'max': 50, 'prompt': 'Early stopping patience'},
            'rnn_hidden_size': {'type': int, 'default': 64, 'min': 32, 'max': 256, 'prompt': 'RNN hidden size'}
        },
        'fixed_params': {'device': 'auto'}
    }

if RandomForest is not None:
    MODEL_REGISTRY['rf'] = {
        'name': 'Random Forest',
        'model_class': RandomForest,
        'type': 'shallow',
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 100, 'min': 10, 'max': 500, 'prompt': 'Number of estimators'},
            'max_depth': {'type': int, 'default': None, 'min': 3, 'max': 50, 'prompt': 'Maximum depth'},
            'min_samples_split': {'type': int, 'default': 2, 'min': 2, 'max': 20, 'prompt': 'Min samples split'},
            'min_samples_leaf': {'type': int, 'default': 1, 'min': 1, 'max': 10, 'prompt': 'Min samples leaf'}
        },
        'fixed_params': {'random_state': 42}
    }

if XGBoost is not None:
    MODEL_REGISTRY['xgboost'] = {
        'name': 'XGBoost',
        'model_class': XGBoost,
        'type': 'shallow',
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 100, 'min': 10, 'max': 500, 'prompt': 'Number of estimators'},
            'learning_rate': {'type': float, 'default': 0.1, 'min': 0.01, 'max': 0.3, 'prompt': 'Learning rate'},
            'max_depth': {'type': int, 'default': 6, 'min': 3, 'max': 15, 'prompt': 'Maximum depth'},
            'subsample': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': 'Subsample ratio'},
            'colsample_bytree': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': 'Column sample ratio'}
        },
        'fixed_params': {'random_state': 42}
    }

if SVM is not None:
    MODEL_REGISTRY['svm'] = {
        'name': 'Support Vector Machine',
        'model_class': SVM,
        'type': 'shallow',
        'hyperparameters': {
            'C': {'type': float, 'default': 1.0, 'min': 0.1, 'max': 100.0, 'prompt': 'Regularization parameter'},
            'kernel': {'type': str, 'default': 'rbf', 'options': ['linear', 'poly', 'rbf', 'sigmoid'], 'prompt': 'Kernel type'},
            'gamma': {'type': str, 'default': 'scale', 'options': ['scale', 'auto'], 'prompt': 'Gamma parameter'}
        },
        'fixed_params': {}
    }

if LOCF is not None:
    MODEL_REGISTRY['locf'] = {
        'name': 'Last Observation Carried Forward',
        'model_class': LOCF,
        'type': 'classical',
        'hyperparameters': {},
        'fixed_params': {}
    }

def get_model(model_name: str, model_config: Dict[str, Any]):
    """
    Initializes and returns a model instance from the registry with device selection.

    Args:
        model_name: The name of the model to retrieve (e.g., 'autoencoder').
        model_config: A dictionary of hyperparameters for the model.

    Returns:
        An instance of the requested model, or None if the model is not found.
    """
    if model_name not in MODEL_REGISTRY:
        print(f"❌ Model '{model_name}' not found in registry.")
        return None

    model_info = MODEL_REGISTRY[model_name]
    model_class = model_info['model_class']

    # Prepare configuration with device selection
    final_config = model_config.copy()

    # Handle device selection for deep learning models
    if model_info['type'] in ['deep'] and 'device' in model_info['fixed_params']:
        if model_info['fixed_params']['device'] == 'auto':
            final_config['device'] = detect_device()
        else:
            final_config['device'] = model_info['fixed_params']['device']

    # Add fixed parameters
    for key, value in model_info['fixed_params'].items():
        if key != 'device' or key not in final_config:
            final_config[key] = value

    # Handle model-specific parameter mapping
    try:
        # For shallow models, 'num_models' is a key parameter, derived from 'n_features'
        if model_info['type'] == 'shallow':
            if 'n_features' not in final_config:
                raise ValueError(f"'n_features' must be specified in model_config for {model_name}")

            num_models = final_config.pop('n_features')
            final_config.pop('n_steps', None)  # Shallow models don't use n_steps
            model = model_class(num_models=num_models, **final_config)

        # For PyPOTS deep models, n_steps and n_features are crucial
        elif model_info['type'] == 'deep' and model_name in ['saits', 'transformer', 'brits', 'mrnn']:
            if 'n_steps' not in final_config or 'n_features' not in final_config:
                raise ValueError(f"'n_steps' and 'n_features' must be in model_config for {model_name}")
            model = model_class(**final_config)

        # For other models (UNet, Autoencoder, LOCF)
        else:
            model = model_class(**final_config)

        print(f"✅ Model '{model_info['name']}' ({model_info['type']}) initialized successfully.")
        return model

    except Exception as e:
        print(f"❌ Error initializing model '{model_name}': {e}")
        return None

def train_model(model, train_data: np.ndarray, val_data: Optional[np.ndarray] = None):
    """
    Trains the given model on the provided data.

    Args:
        model: The model instance to be trained.
        train_data: A numpy array of training data.
        val_data: An optional numpy array of validation data.
    """
    print(f"Starting training for model: {model.__class__.__name__}")
    
    # The data needs to be in a dictionary format for the models
    # The 'X' key holds the features, and an indicating_mask can be used for missing values
    train_set = {'X': train_data}
    val_set = {'X': val_data} if val_data is not None else None

    # The fit method is part of the model's API (from BaseImputer or custom)
    model.fit(train_set, val_set)
    print("Training complete.")

def get_models_by_type(model_type: str) -> Dict[str, Dict[str, Any]]:
    """
    Get all models of a specific type from the registry.

    Args:
        model_type: Type of models to retrieve ('shallow', 'deep', 'classical')

    Returns:
        Dictionary of models matching the specified type
    """
    return {k: v for k, v in MODEL_REGISTRY.items() if v['type'] == model_type}

def impute_logs(df, feature_cols, target_col, models_to_run, well_cfg, prediction_mode):
    """
    Main imputation routine with model type branching.

    Args:
        df: Input DataFrame with well log data
        feature_cols: List of feature column names
        target_col: Target column name for imputation
        models_to_run: Dictionary of models to run with their configurations
        well_cfg: Well configuration for training/prediction strategy
        prediction_mode: Prediction mode (1: fill missing, 2: CV, 3: full prediction)

    Returns:
        Tuple of (result_dataframe, model_results)
    """
    # Separate models by type
    shallow_models = {k: v for k, v in models_to_run.items()
                      if MODEL_REGISTRY[k]['type'] == 'shallow'}
    deep_models = {k: v for k, v in models_to_run.items()
                   if MODEL_REGISTRY[k]['type'] == 'deep'}
    classical_models = {k: v for k, v in models_to_run.items()
                        if MODEL_REGISTRY[k]['type'] == 'classical'}

    results = {}
    result_df = df.copy()

    print(f"🎯 Target Log: {target_col}")
    print(f"📊 Models to run: {len(models_to_run)} ({len(shallow_models)} shallow, {len(deep_models)} deep, {len(classical_models)} classical)")

    # Run shallow models if any
    if shallow_models:
        print("\n🔄 Running shallow learning models...")
        shallow_results = run_shallow_imputation(df, feature_cols, target_col,
                                                shallow_models, well_cfg, prediction_mode)
        results.update(shallow_results)

    # Run deep models if any
    if deep_models:
        print("\n🧠 Running deep learning models...")
        deep_results = run_deep_imputation(df, feature_cols, target_col,
                                          deep_models, well_cfg, prediction_mode)
        results.update(deep_results)

    # Run classical models if any
    if classical_models:
        print("\n📊 Running classical models...")
        classical_results = run_classical_imputation(df, feature_cols, target_col,
                                                    classical_models, well_cfg, prediction_mode)
        results.update(classical_results)

    return result_df, results

def run_shallow_imputation(df, feature_cols, target_col, models_to_run, well_cfg, prediction_mode):
    """
    Run shallow learning models for imputation.

    Args:
        df: Input DataFrame
        feature_cols: List of feature columns
        target_col: Target column for imputation
        models_to_run: Dictionary of shallow models to run
        well_cfg: Well configuration
        prediction_mode: Prediction mode

    Returns:
        Dictionary of model results
    """
    results = {}

    # Prepare data for shallow models
    feat_set = feature_cols + ['MD']

    # Train data selection
    if well_cfg['mode'] == 'separated':
        train_df = df[df['WELL'].isin(well_cfg['training_wells'])]
    else:
        train_df = df

    train = train_df[train_df[target_col].notna()].copy()

    if train.empty:
        print("❌ No training data available for shallow models.")
        return results

    # Prepare features and target
    X = train[feat_set].apply(lambda c: c.fillna(c.mean()), axis=0)
    y = train[target_col]
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.25, random_state=42)

    # Train and evaluate each shallow model
    for model_name, model_config in models_to_run.items():
        try:
            print(f"🔄 Training {MODEL_REGISTRY[model_name]['name']}...")

            # Add required parameters for shallow models
            model_config_with_features = model_config.copy()
            model_config_with_features['n_features'] = len(feature_cols)

            model = get_model(model_name, model_config_with_features)
            if model is None:
                continue

            # Note: Actual training implementation will be completed in Phase 2
            print(f"✅ {MODEL_REGISTRY[model_name]['name']} initialized successfully.")
            print("⏳ Full training pipeline to be implemented in Phase 2...")

            results[model_name] = {
                'model': model,
                'type': 'shallow',
                'status': 'initialized',
                'note': 'Training pipeline to be completed in Phase 2'
            }

        except Exception as e:
            print(f"❌ Error training {model_name}: {e}")
            results[model_name] = {
                'model': None,
                'type': 'shallow',
                'status': 'failed',
                'error': str(e)
            }

    return results

def run_deep_imputation(df, feature_cols, target_col, models_to_run, well_cfg, prediction_mode):
    """
    Run deep learning models for imputation with complete workflow.

    Args:
        df: Input DataFrame
        feature_cols: List of feature columns
        target_col: Target column for imputation
        models_to_run: Dictionary of deep models to run
        well_cfg: Well configuration
        prediction_mode: Prediction mode

    Returns:
        Dictionary of model results
    """
    results = {}

    print("🧠 Running deep learning models with complete workflow...")

    # Validate data for deep learning
    from .data_handler import validate_data_quality, handle_sequence_edge_cases

    # Check data quality
    warnings_list = validate_data_quality(df, feature_cols, target_col)
    if len(warnings_list) > 3:
        print("⚠️ Multiple data quality issues detected - deep learning may not perform well")

    for model_name, model_config in models_to_run.items():
        try:
            print(f"\n🔄 Running {MODEL_REGISTRY[model_name]['name']}...")

            # Add model name to config for the complete workflow
            model_config_complete = model_config.copy()
            model_config_complete['model_name'] = model_name

            # Check sequence length compatibility
            sequence_length = model_config.get('sequence_length', 64)
            short_wells, recommendation = handle_sequence_edge_cases(df, sequence_length)

            if len(short_wells) > 0:
                print(f"⚠️ {len(short_wells)} wells are shorter than sequence length")
                if len(short_wells) / df['WELL'].nunique() > 0.5:
                    print("❌ Too many short wells - skipping this model")
                    results[model_name] = {
                        'model': None,
                        'type': 'deep',
                        'status': 'skipped',
                        'reason': 'Too many wells shorter than sequence length',
                        'recommendation': recommendation
                    }
                    continue

            # Run complete deep learning workflow
            reconstructed_predictions, trained_model = run_deep_imputation_complete(
                df, feature_cols, target_col, model_config_complete, well_cfg
            )

            if trained_model is not None and reconstructed_predictions:
                print(f"✅ {MODEL_REGISTRY[model_name]['name']} completed successfully")

                results[model_name] = {
                    'model': trained_model,
                    'type': 'deep',
                    'status': 'completed',
                    'predictions': reconstructed_predictions,
                    'wells_processed': list(reconstructed_predictions.keys()),
                    'sequence_length': sequence_length,
                    'data_quality_warnings': len(warnings_list)
                }
            else:
                print(f"❌ {MODEL_REGISTRY[model_name]['name']} failed during training/prediction")
                results[model_name] = {
                    'model': None,
                    'type': 'deep',
                    'status': 'failed',
                    'error': 'Training or prediction failed'
                }

        except Exception as e:
            print(f"❌ Error running {model_name}: {e}")
            results[model_name] = {
                'model': None,
                'type': 'deep',
                'status': 'failed',
                'error': str(e)
            }

    return results

def run_classical_imputation(df, feature_cols, target_col, models_to_run, well_cfg, prediction_mode):
    """
    Run classical imputation models.

    Args:
        df: Input DataFrame
        feature_cols: List of feature columns
        target_col: Target column for imputation
        models_to_run: Dictionary of classical models to run
        well_cfg: Well configuration
        prediction_mode: Prediction mode

    Returns:
        Dictionary of model results
    """
    results = {}

    for model_name, model_config in models_to_run.items():
        try:
            print(f"📊 Running {MODEL_REGISTRY[model_name]['name']}...")

            model = get_model(model_name, model_config)
            if model is None:
                continue

            print(f"✅ {MODEL_REGISTRY[model_name]['name']} completed.")

            results[model_name] = {
                'model': model,
                'type': 'classical',
                'status': 'completed'
            }

        except Exception as e:
            print(f"❌ Error running {model_name}: {e}")
            results[model_name] = {
                'model': None,
                'type': 'classical',
                'status': 'failed',
                'error': str(e)
            }

    return results

# ============================================================================
# PHASE 3: SEQUENCE RECONSTRUCTION AND DEEP LEARNING WORKFLOW
# ============================================================================

def reconstruct_from_sequences(predictions: np.ndarray, metadata: List[Dict],
                              original_shape: Tuple[int, ...]) -> Dict[str, np.ndarray]:
    """
    Reconstruct continuous logs from overlapping sequence predictions as per guidelines.

    Args:
        predictions: Array of sequence predictions [n_sequences, sequence_length]
        metadata: List of metadata dictionaries for each sequence
        original_shape: Shape of the original data

    Returns:
        Dictionary mapping well names to reconstructed arrays
    """
    print("🔄 Reconstructing continuous logs from sequences...")

    # Initialize arrays for accumulation
    reconstructed = {}
    counts = {}

    # Group metadata by well
    wells = set(m['well'] for m in metadata)

    for well in wells:
        well_metadata = [m for m in metadata if m['well'] == well]
        if not well_metadata:
            continue

        # Determine the maximum index for this well
        max_idx = max(m['end_idx'] for m in well_metadata)

        reconstructed[well] = np.zeros(max_idx)
        counts[well] = np.zeros(max_idx)

    # Accumulate predictions with overlap handling
    for i, (pred, meta) in enumerate(zip(predictions, metadata)):
        well = meta['well']
        start = meta['start_idx']
        end = meta['end_idx']

        if well not in reconstructed:
            continue

        # Handle different prediction shapes
        if pred.ndim == 1:
            pred_values = pred
        elif pred.ndim == 2:
            # If prediction is 2D, take the target column (assuming last column)
            pred_values = pred[:, -1] if pred.shape[1] > 1 else pred.flatten()
        else:
            pred_values = pred.flatten()

        # Ensure we don't exceed array bounds
        actual_end = min(end, len(reconstructed[well]))
        actual_length = actual_end - start
        pred_length = min(len(pred_values), actual_length)

        if pred_length > 0:
            reconstructed[well][start:start + pred_length] += pred_values[:pred_length]
            counts[well][start:start + pred_length] += 1

    # Average overlapping predictions
    for well in reconstructed:
        mask = counts[well] > 0
        reconstructed[well][mask] /= counts[well][mask]

        # Handle areas with no predictions (set to NaN)
        reconstructed[well][~mask] = np.nan

        print(f"✅ Reconstructed {well}: {mask.sum()}/{len(mask)} points covered")

    print(f"✅ Sequence reconstruction completed for {len(reconstructed)} wells")
    return reconstructed

def run_deep_imputation_complete(df: pd.DataFrame, feature_cols: List[str], target_col: str,
                               model_config: Dict[str, Any], well_cfg: Dict[str, Any]) -> Tuple[Dict[str, np.ndarray], Any]:
    """
    Complete deep learning workflow as per guidelines.

    Args:
        df: Input DataFrame
        feature_cols: List of feature column names
        target_col: Target column name
        model_config: Model configuration dictionary
        well_cfg: Well configuration

    Returns:
        Tuple of (reconstructed_predictions, trained_model)
    """
    from .data_handler import create_scaling_pipeline, create_sequences, inverse_transform_predictions

    print("🧠 Starting complete deep learning imputation workflow...")

    # Step 1: Preprocessing
    print("📊 Step 1: Data preprocessing...")
    scaled_df, scalers = create_scaling_pipeline(df, feature_cols, target_col, strategy='standard')

    # Step 2: Sequence creation
    print("🔗 Step 2: Creating sequences...")
    sequence_length = model_config.get('sequence_length', 64)
    stride = model_config.get('stride', sequence_length // 2)  # 50% overlap by default

    sequences, targets, metadata = create_sequences(
        scaled_df, 'WELL', feature_cols, target_col,
        sequence_length=sequence_length, stride=stride
    )

    if len(sequences) == 0:
        print("❌ No sequences created - cannot proceed with deep learning")
        return {}, None

    # Step 3: Train/validation split
    print("🔀 Step 3: Creating train/validation split...")
    train_idx, val_idx = train_test_split(range(len(sequences)), test_size=0.2, random_state=42)

    # Step 4: Model training
    print("🏋️ Step 4: Training deep learning model...")
    model_name = model_config.get('model_name', 'autoencoder')

    # Prepare model configuration
    model_config_with_params = model_config.copy()
    model_config_with_params['n_features'] = len(feature_cols)
    model_config_with_params['n_steps'] = sequence_length

    model = get_model(model_name, model_config_with_params)
    if model is None:
        print("❌ Model initialization failed")
        return {}, None

    # Prepare training data in PyPOTS format
    train_data = {
        'X': sequences[train_idx],
        'indicating_mask': ~np.isnan(sequences[train_idx])
    }

    val_data = {
        'X': sequences[val_idx],
        'indicating_mask': ~np.isnan(sequences[val_idx])
    } if len(val_idx) > 0 else None

    # Train the model
    try:
        model.fit(train_data, val_data)
        print("✅ Model training completed successfully")
    except Exception as e:
        print(f"❌ Model training failed: {e}")
        return {}, None

    # Step 5: Prediction on all sequences
    print("🔮 Step 5: Making predictions...")
    prediction_data = {
        'X': sequences,
        'indicating_mask': ~np.isnan(sequences)
    }

    try:
        predictions_dict = model.predict(prediction_data)
        predictions = predictions_dict['imputation']
        print("✅ Predictions completed successfully")
    except Exception as e:
        print(f"❌ Prediction failed: {e}")
        return {}, None

    # Step 6: Sequence reconstruction
    print("🔧 Step 6: Reconstructing sequences...")
    reconstructed = reconstruct_from_sequences(predictions, metadata, df.shape)

    # Step 7: Inverse transform to original scale
    print("↩️ Step 7: Inverse transforming to original scale...")
    for well in reconstructed:
        reconstructed[well] = inverse_transform_predictions(
            reconstructed[well], scalers, target_col
        )

    print("✅ Complete deep learning workflow finished successfully")
    return reconstructed, model

def predict_with_model(model, data: np.ndarray) -> np.ndarray:
    """
    Makes predictions using the trained model.

    Args:
        model: The trained model instance.
        data: A numpy array of data to make predictions on.

    Returns:
        A numpy array containing the imputed data.
    """
    print("Making predictions...")

    # Prepare data for prediction
    prediction_set = {'X': data}

    # The predict method returns a dictionary with the imputation results
    imputed_data_dict = model.predict(prediction_set)

    # The imputed values are under the 'imputation' key
    imputed_data = imputed_data_dict['imputation']
    print("Prediction complete.")
    return imputed_data
