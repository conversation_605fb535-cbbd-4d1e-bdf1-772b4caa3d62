'''
This module handles loading, saving, and managing configuration files.
'''

import j<PERSON>
from typing import Dict, Any

# Define default configuration settings
DEFAULT_CONFIG = {
    "data_settings": {
        "las_folder": "./data/las_files/",
        "selected_logs": ["GR", "RHOB", "NPHI", "DTC"],
        "std_names": {
            "GR": "GR",
            "RHOB": "RHOB",
            "NPHI": "NPHI",
            "DTC": "DTC"
        }
    },
    "processing_settings": {
        "slice_size": 256,
        "slice_stride": 128
    },
    "model_settings": {
        "name": "autoencoder",
        "hyperparameters": {
            "autoencoder": {
                "epochs": 50,
                "patience": 10,
                "batch_size": 32
            },
            "unet": {
                "epochs": 50,
                "patience": 10,
                "batch_size": 32
            },
            "rf": {},
            "xgboost": {},
            "svm": {},
            "locf": {},
            "saits": {
                "epochs": 100,
                "patience": 10,
                "batch_size": 32,
                "n_layers": 2,
                "d_model": 256,
                "d_inner": 128,
                "n_head": 4,
                "d_k": 64,
                "d_v": 64,
                "dropout": 0.1
            },
            "transformer": {
                "epochs": 100,
                "patience": 10,
                "batch_size": 32,
                "n_layers": 2,
                "d_model": 256,
                "d_inner": 128,
                "n_head": 4,
                "d_k": 64,
                "d_v": 64,
                "dropout": 0.1
            },
            "brits": {
                "epochs": 100,
                "patience": 10,
                "batch_size": 32,
                "rnn_hidden_size": 64
            },
            "mrnn": {
                "epochs": 100,
                "patience": 10,
                "batch_size": 32,
                "rnn_hidden_size": 64
            }
        }
    },
    "output_settings": {
        "save_path": "./results/",
        "report_name": "imputation_report.pdf"
    }
}

def load_config(config_path: str) -> Dict[str, Any]:
    """
    Loads a configuration from a JSON file.
    If the file does not exist, it returns the default configuration.

    Args:
        config_path: The path to the configuration file.

    Returns:
        A dictionary containing the configuration settings.
    """
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        # For any missing top-level keys, add them from the default config
        for key, value in DEFAULT_CONFIG.items():
            if key not in config:
                config[key] = value
        return config
    except FileNotFoundError:
        print(f"Configuration file not found at {config_path}. Using default configuration.")
        return DEFAULT_CONFIG
    except json.JSONDecodeError:
        print(f"Error decoding JSON from {config_path}. Using default configuration.")
        return DEFAULT_CONFIG

def save_config(config: Dict[str, Any], config_path: str):
    """
    Saves the given configuration to a JSON file.

    Args:
        config: The configuration dictionary to save.
        config_path: The path where the configuration file will be saved.
    """
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        print(f"Configuration saved to {config_path}")
    except IOError as e:
        print(f"Error saving configuration to {config_path}: {e}")

def get_model_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extracts the model-specific hyperparameters from the main configuration.

    Args:
        config: The main configuration dictionary.

    Returns:
        A dictionary of hyperparameters for the selected model.
    """
    model_name = config.get('model_settings', {}).get('name', DEFAULT_CONFIG['model_settings']['name']).lower()
    all_hyperparams = config.get('model_settings', {}).get('hyperparameters', {})
    
    # Get the specific config for the selected model
    model_specific_config = all_hyperparams.get(model_name, {})
    
    return model_specific_config
