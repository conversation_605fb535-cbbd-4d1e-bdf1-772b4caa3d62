#!/usr/bin/env python3
"""
Test script to verify that the masked_fill import fix works correctly.
"""

import numpy as np

def masked_fill(data, mask, fill_value):
    """
    Fill values in data array where mask is True with fill_value.

    This function replaces the deprecated pypots.data.masked_fill function.

    Args:
        data: Input array to be filled
        mask: Boolean mask indicating where to fill values (can be float or boolean)
        fill_value: Value to fill where mask is True

    Returns:
        Array with values filled according to mask
    """
    result = data.copy()
    # Convert mask to boolean if it's not already boolean
    # This handles cases where mask is float32 (like from missing_mask operations)
    if mask.dtype != bool:
        boolean_mask = mask.astype(bool)
    else:
        boolean_mask = mask
    result[boolean_mask] = fill_value
    return result

def test_masked_fill():
    """Test the masked_fill function with various scenarios."""
    print("Testing masked_fill function...")
    
    # Test 1: Basic functionality
    data = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
    mask = np.array([[True, False, True], [False, True, False]])
    result = masked_fill(data, mask, np.nan)
    
    expected = np.array([[np.nan, 2.0, np.nan], [4.0, np.nan, 6.0]])
    
    # Check if the result matches expected (accounting for NaN comparison)
    mask_nan_result = np.isnan(result)
    mask_nan_expected = np.isnan(expected)
    
    if np.array_equal(mask_nan_result, mask_nan_expected) and np.array_equal(result[~mask_nan_result], expected[~mask_nan_expected]):
        print("✓ Test 1 passed: Basic functionality works correctly")
    else:
        print("✗ Test 1 failed: Basic functionality not working")
        print(f"Expected: {expected}")
        print(f"Got: {result}")
        return False
    
    # Test 2: Test with missing_mask scenario (like in main.py)
    X = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
    missing_mask = np.array([[False, True, False], [True, False, True]])
    
    # This is how it's used in main.py: masked_fill(X, 1 - missing_mask, np.nan)
    result = masked_fill(X, 1 - missing_mask, np.nan)
    
    # Where missing_mask is False, (1 - missing_mask) is True, so those should be filled with NaN
    expected_mask = ~missing_mask  # Invert the missing_mask
    expected = X.copy()
    expected[expected_mask] = np.nan
    
    mask_nan_result = np.isnan(result)
    mask_nan_expected = np.isnan(expected)
    
    if np.array_equal(mask_nan_result, mask_nan_expected) and np.array_equal(result[~mask_nan_result], expected[~mask_nan_expected]):
        print("✓ Test 2 passed: Missing mask scenario works correctly")
    else:
        print("✗ Test 2 failed: Missing mask scenario not working")
        print(f"Expected: {expected}")
        print(f"Got: {result}")
        return False
    
    print("All tests passed! The masked_fill function is working correctly.")
    return True

if __name__ == "__main__":
    test_masked_fill()
